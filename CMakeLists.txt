cmake_minimum_required(VERSION 3.20)
project(SchoolManagementSystem)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find SQLite3 - try different approaches for Windows
if(WIN32)
    # For Windows, we'll use a simpler approach
    find_library(SQLITE3_LIBRARIES NAMES sqlite3 PATHS ${CMAKE_CURRENT_SOURCE_DIR}/lib)
    if(NOT SQLITE3_LIBRARIES)
        # If not found, we'll link it directly
        set(SQLITE3_LIBRARIES sqlite3)
    endif()
else()
    # For Unix-like systems
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(SQLITE3 sqlite3)
    endif()
    if(NOT SQLITE3_FOUND)
        find_library(SQLITE3_LIBRARIES NAMES sqlite3)
    endif()
endif()

# Source files
set(SOURCES
    src/main.cpp
    src/student.cpp
    src/teacher.cpp
    src/course.cpp
    src/grade.cpp
    src/database.cpp
)

# Header files
set(HEADERS
    include/student.h
    include/teacher.h
    include/course.h
    include/grade.h
    include/database.h
)

# Include directories
include_directories(include)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Link SQLite3
if(SQLITE3_LIBRARIES)
    target_link_libraries(${PROJECT_NAME} ${SQLITE3_LIBRARIES})
    if(SQLITE3_CFLAGS_OTHER)
        target_compile_options(${PROJECT_NAME} PRIVATE ${SQLITE3_CFLAGS_OTHER})
    endif()
else()
    # Fallback: try to link sqlite3 directly
    target_link_libraries(${PROJECT_NAME} sqlite3)
endif()

# Compiler flags
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE /W4)
else()
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Set output directory
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Installation
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

install(DIRECTORY config/
    DESTINATION share/${PROJECT_NAME}/config
    FILES_MATCHING PATTERN "*.ini"
)
