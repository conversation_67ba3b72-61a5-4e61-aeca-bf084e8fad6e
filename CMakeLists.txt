cmake_minimum_required(VERSION 3.20)
project(SchoolManagementSystem)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Use local SQLite3 stub implementation
message(STATUS "Using local SQLite3 stub implementation")

# Source files
set(SOURCES
    src/main.cpp
    src/student.cpp
    src/teacher.cpp
    src/course.cpp
    src/grade.cpp
    src/database.cpp
    src/sqlite3_stub.cpp
)

# Header files
set(HEADERS
    include/student.h
    include/teacher.h
    include/course.h
    include/grade.h
    include/database.h
    include/sqlite3.h
)

# Include directories
include_directories(include)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# No external SQLite3 linking needed - using stub implementation

# Compiler flags
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE /W4)
else()
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Set output directory
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Installation
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

install(DIRECTORY config/
    DESTINATION share/${PROJECT_NAME}/config
    FILES_MATCHING PATTERN "*.ini"
)
