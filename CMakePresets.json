{"version": 3, "configurePresets": [{"name": "default", "displayName": "<PERSON><PERSON><PERSON>g", "description": "Default build configuration", "generator": "MinGW Makefiles", "binaryDir": "${sourceDir}/build/${presetName}", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "release", "displayName": "Release Config", "description": "Release build configuration", "generator": "MinGW Makefiles", "binaryDir": "${sourceDir}/build/${presetName}", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}], "buildPresets": [{"name": "default", "configurePreset": "default"}, {"name": "release", "configurePreset": "release"}]}