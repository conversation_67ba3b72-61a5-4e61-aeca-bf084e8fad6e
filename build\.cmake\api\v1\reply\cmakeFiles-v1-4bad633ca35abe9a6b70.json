{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU-C-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/6_ school/build", "source": "C:/6_ school"}, "version": {"major": 1, "minor": 1}}