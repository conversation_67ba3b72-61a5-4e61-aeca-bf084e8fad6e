{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-5e4f3cc228b86912ae63.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "SchoolManagementSystem", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "SchoolManagementSystem::@6890427a1f51a3e7e1df", "jsonFile": "target-SchoolManagementSystem-Debug-d7a892e9a0eec06ff374.json", "name": "SchoolManagementSystem", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/6_ school/build", "source": "C:/6_ school"}, "version": {"major": 2, "minor": 8}}