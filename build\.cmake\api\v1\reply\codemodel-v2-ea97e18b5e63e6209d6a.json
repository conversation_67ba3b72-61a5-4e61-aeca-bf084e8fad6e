{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-dff4efcf111ca15a5715.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "SchoolManagementSystem", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "SchoolManagementSystem::@6890427a1f51a3e7e1df", "jsonFile": "target-SchoolManagementSystem-Debug-9ec30d1df7a40d4bb780.json", "name": "SchoolManagementSystem", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/6_ school/build", "source": "C:/6_ school"}, "version": {"major": 2, "minor": 8}}