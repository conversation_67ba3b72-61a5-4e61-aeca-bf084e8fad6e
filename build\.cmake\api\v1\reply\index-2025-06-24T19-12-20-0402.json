{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/bin/cmake.exe", "cpack": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/bin/cpack.exe", "ctest": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/bin/ctest.exe", "root": "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 3, "string": "4.0.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-ea97e18b5e63e6209d6a.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-5c122d50eae26eb04220.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-4bad633ca35abe9a6b70.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-d0831a2381cf913eba11.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-5c122d50eae26eb04220.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-ea97e18b5e63e6209d6a.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-d0831a2381cf913eba11.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-4bad633ca35abe9a6b70.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}