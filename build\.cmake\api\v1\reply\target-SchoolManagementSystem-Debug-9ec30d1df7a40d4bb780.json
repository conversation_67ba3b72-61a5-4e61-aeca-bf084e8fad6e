{"artifacts": [{"path": "bin/SchoolManagementSystem.exe"}, {"path": "bin/SchoolManagementSystem.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "target_compile_options", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 49, "parent": 0}, {"command": 1, "file": 0, "line": 75, "parent": 0}, {"command": 2, "file": 0, "line": 53, "parent": 0}, {"command": 3, "file": 0, "line": 66, "parent": 0}, {"command": 4, "file": 0, "line": 46, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}, {"backtrace": 4, "fragment": "-Wall"}, {"backtrace": 4, "fragment": "-Wextra"}, {"backtrace": 4, "fragment": "-Wpedantic"}], "includes": [{"backtrace": 5, "path": "C:/6_ school/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "id": "SchoolManagementSystem::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/SchoolManagementSystem"}}, "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 3, "fragment": "-lsqlite3", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "SchoolManagementSystem", "nameOnDisk": "SchoolManagementSystem.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [6, 7, 8, 9, 10]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/student.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/teacher.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/course.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/grade.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/database.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/student.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/teacher.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/course.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/grade.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/database.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}