{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include", "C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed", "C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include"], "linkDirectories": ["C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0", "C:/Users/<USER>/mingw64/lib/gcc", "C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib", "C:/Users/<USER>/mingw64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["mingw32", "gcc", "mingwex", "kernel32", "mcfgth<PERSON>", "kernel32", "ntdll", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc", "mingwex", "kernel32", "mcfgth<PERSON>", "kernel32", "ntdll"]}, "path": "C:/Users/<USER>/mingw64/bin/cc.exe", "version": "15.1.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++", "C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32", "C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward", "C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include", "C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed", "C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include"], "linkDirectories": ["C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0", "C:/Users/<USER>/mingw64/lib/gcc", "C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib", "C:/Users/<USER>/mingw64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "mingw32", "gcc_s", "gcc", "mingwex", "kernel32", "mcfgth<PERSON>", "kernel32", "ntdll", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc_s", "gcc", "mingwex", "kernel32", "mcfgth<PERSON>", "kernel32", "ntdll"]}, "path": "C:/Users/<USER>/mingw64/bin/c++.exe", "version": "15.1.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}, {"compiler": {"implicit": {}, "path": "C:/Users/<USER>/mingw64/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}