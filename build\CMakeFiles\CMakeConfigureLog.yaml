
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Users/<USER>/mingw64/bin/cc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        C:/6_ school/build/CMakeFiles/4.0.3/CompilerIdC/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Users/<USER>/mingw64/bin/c++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        C:/6_ school/build/CMakeFiles/4.0.3/CompilerIdCXX/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-ihsw3z"
      binary: "C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-ihsw3z"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-ihsw3z'
        
        Run Build Command(s): "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/mingw64/bin/mingw32-make.exe -f Makefile cmTC_63ff9/fast
        C:/Users/<USER>/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_63ff9.dir\\build.make CMakeFiles/cmTC_63ff9.dir/build
        mingw32-make[1]: Entering directory 'C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-ihsw3z'
        Building C object CMakeFiles/cmTC_63ff9.dir/CMakeCCompilerABI.c.obj
        C:\\Users\\<USER>\\mingw64\\bin\\cc.exe   -v -o CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.obj -c "C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\mingw64\\bin\\cc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: mcf
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (x86_64-mcf-seh-rev0, Built by MinGW-Builds project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_63ff9.dir\\'
         C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1.exe -quiet -v -iprefix C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -U_REENTRANT C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_63ff9.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccaTrljf.s
        GNU C23 (x86_64-mcf-seh-rev0, Built by MinGW-Builds project) version 15.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.1.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include"
        ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"
        ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: e803e06658f47391fb2e2904b5bc0929
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_63ff9.dir\\'
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccaTrljf.s
        GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44
        COMPILER_PATH=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/mingw64/bin/../libexec/gcc/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/mingw64/bin/../lib/gcc/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.'
        Linking C executable cmTC_63ff9.exe
        "C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_63ff9.dir\\link.txt --verbose=1
        "C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_63ff9.dir/objects.a
        C:\\Users\\<USER>\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_63ff9.dir/objects.a @CMakeFiles\\cmTC_63ff9.dir\\objects1.rsp
        C:\\Users\\<USER>\\mingw64\\bin\\cc.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_63ff9.dir/objects.a -Wl,--no-whole-archive -o cmTC_63ff9.exe -Wl,--out-implib,libcmTC_63ff9.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\mingw64\\bin\\cc.exe
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: mcf
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (x86_64-mcf-seh-rev0, Built by MinGW-Builds project) 
        COMPILER_PATH=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/mingw64/bin/../libexec/gcc/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/mingw64/bin/../lib/gcc/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_63ff9.exe' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_63ff9.'
         C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKHIG7U.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll --sysroot=C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_63ff9.exe C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/mingw64/bin/../lib/gcc -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_63ff9.dir/objects.a --no-whole-archive --out-implib libcmTC_63ff9.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        collect2 version 15.1.0
        C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKHIG7U.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll --sysroot=C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_63ff9.exe C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/mingw64/bin/../lib/gcc -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_63ff9.dir/objects.a --no-whole-archive --out-implib libcmTC_63ff9.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        GNU ld (GNU Binutils) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_63ff9.exe' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_63ff9.'
        mingw32-make[1]: Leaving directory 'C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-ihsw3z'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
          add: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
          add: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        collapse include dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        collapse include dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include] ==> [C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include]
        implicit include dirs: [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include;C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed;C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-ihsw3z']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/mingw64/bin/mingw32-make.exe -f Makefile cmTC_63ff9/fast]
        ignore line: [C:/Users/<USER>/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_63ff9.dir\\build.make CMakeFiles/cmTC_63ff9.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-ihsw3z']
        ignore line: [Building C object CMakeFiles/cmTC_63ff9.dir/CMakeCCompilerABI.c.obj]
        ignore line: [C:\\Users\\<USER>\\mingw64\\bin\\cc.exe   -v -o CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.obj -c "C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\mingw64\\bin\\cc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: mcf]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (x86_64-mcf-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_63ff9.dir\\']
        ignore line: [ C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1.exe -quiet -v -iprefix C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -U_REENTRANT C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_63ff9.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccaTrljf.s]
        ignore line: [GNU C23 (x86_64-mcf-seh-rev0  Built by MinGW-Builds project) version 15.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.1.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: e803e06658f47391fb2e2904b5bc0929]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_63ff9.dir\\']
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccaTrljf.s]
        ignore line: [GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_63ff9.dir\\CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_63ff9.exe]
        ignore line: ["C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_63ff9.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_63ff9.dir/objects.a]
        ignore line: [C:\\Users\\<USER>\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_63ff9.dir/objects.a @CMakeFiles\\cmTC_63ff9.dir\\objects1.rsp]
        ignore line: [C:\\Users\\<USER>\\mingw64\\bin\\cc.exe  -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_63ff9.dir/objects.a -Wl --no-whole-archive -o cmTC_63ff9.exe -Wl --out-implib libcmTC_63ff9.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\mingw64\\bin\\cc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: mcf]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (x86_64-mcf-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_63ff9.exe' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_63ff9.']
        link line: [ C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKHIG7U.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll --sysroot=C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_63ff9.exe C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/mingw64/bin/../lib/gcc -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_63ff9.dir/objects.a --no-whole-archive --out-implib libcmTC_63ff9.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKHIG7U.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmcfgthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lntdll] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-liconv] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmcfgthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lntdll] ==> ignore
          arg [--sysroot=C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_63ff9.exe] ==> ignore
          arg [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_63ff9.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_63ff9.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmcfgthread] ==> lib [mcfgthread]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lntdll] ==> lib [ntdll]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmcfgthread] ==> lib [mcfgthread]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lntdll] ==> lib [ntdll]
          arg [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        linker tool for 'C': ../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
        collapse obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc] ==> [C:/Users/<USER>/mingw64/lib/gcc]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [C:/Users/<USER>/mingw64/lib]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [C:/Users/<USER>/mingw64/lib]
        implicit libs: [mingw32;gcc;mingwex;kernel32;mcfgthread;kernel32;ntdll;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc;mingwex;kernel32;mcfgthread;kernel32;ntdll]
        implicit objs: [C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib/crt2.o;C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o;C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        implicit dirs: [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;C:/Users/<USER>/mingw64/lib/gcc;C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib;C:/Users/<USER>/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "--version"
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-np4f57"
      binary: "C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-np4f57"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-np4f57'
        
        Run Build Command(s): "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/mingw64/bin/mingw32-make.exe -f Makefile cmTC_4e794/fast
        C:/Users/<USER>/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_4e794.dir\\build.make CMakeFiles/cmTC_4e794.dir/build
        mingw32-make[1]: Entering directory 'C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-np4f57'
        Building CXX object CMakeFiles/cmTC_4e794.dir/CMakeCXXCompilerABI.cpp.obj
        C:\\Users\\<USER>\\mingw64\\bin\\c++.exe   -v -o CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\mingw64\\bin\\c++.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: mcf
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (x86_64-mcf-seh-rev0, Built by MinGW-Builds project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4e794.dir\\'
         C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1plus.exe -quiet -v -iprefix C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -U_REENTRANT C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_4e794.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccElqnhF.s
        GNU C++17 (x86_64-mcf-seh-rev0, Built by MinGW-Builds project) version 15.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.1.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++"
        ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32"
        ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward"
        ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include"
        ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"
        ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: 582922735da9f28a737c24bededc050b
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4e794.dir\\'
         C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccElqnhF.s
        GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44
        COMPILER_PATH=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/mingw64/bin/../libexec/gcc/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/mingw64/bin/../lib/gcc/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_4e794.exe
        "C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_4e794.dir\\link.txt --verbose=1
        "C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_4e794.dir/objects.a
        C:\\Users\\<USER>\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_4e794.dir/objects.a @CMakeFiles\\cmTC_4e794.dir\\objects1.rsp
        C:\\Users\\<USER>\\mingw64\\bin\\c++.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_4e794.dir/objects.a -Wl,--no-whole-archive -o cmTC_4e794.exe -Wl,--out-implib,libcmTC_4e794.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\mingw64\\bin\\c++.exe
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: mcf
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (x86_64-mcf-seh-rev0, Built by MinGW-Builds project) 
        COMPILER_PATH=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/mingw64/bin/../libexec/gcc/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/Users/<USER>/mingw64/bin/../lib/gcc/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4e794.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_4e794.'
         C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccoNYo7g.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll --sysroot=C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_4e794.exe C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/mingw64/bin/../lib/gcc -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_4e794.dir/objects.a --no-whole-archive --out-implib libcmTC_4e794.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        collect2 version 15.1.0
        C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccoNYo7g.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll --sysroot=C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_4e794.exe C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/mingw64/bin/../lib/gcc -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_4e794.dir/objects.a --no-whole-archive --out-implib libcmTC_4e794.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        GNU ld (GNU Binutils) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4e794.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_4e794.'
        mingw32-make[1]: Leaving directory 'C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-np4f57'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++]
          add: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32]
          add: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward]
          add: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
          add: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
          add: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++]
        collapse include dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32]
        collapse include dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward]
        collapse include dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        collapse include dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        collapse include dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include] ==> [C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include]
        implicit include dirs: [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++;C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32;C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward;C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include;C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed;C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-np4f57']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/bin/cmake.exe" -E env VERBOSE=1 C:/Users/<USER>/mingw64/bin/mingw32-make.exe -f Makefile cmTC_4e794/fast]
        ignore line: [C:/Users/<USER>/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_4e794.dir\\build.make CMakeFiles/cmTC_4e794.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'C:/6_ school/build/CMakeFiles/CMakeScratch/TryCompile-np4f57']
        ignore line: [Building CXX object CMakeFiles/cmTC_4e794.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [C:\\Users\\<USER>\\mingw64\\bin\\c++.exe   -v -o CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\mingw64\\bin\\c++.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: mcf]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (x86_64-mcf-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4e794.dir\\']
        ignore line: [ C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1plus.exe -quiet -v -iprefix C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -U_REENTRANT C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_4e794.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccElqnhF.s]
        ignore line: [GNU C++17 (x86_64-mcf-seh-rev0  Built by MinGW-Builds project) version 15.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.1.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++]
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32]
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward]
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 582922735da9f28a737c24bededc050b]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4e794.dir\\']
        ignore line: [ C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccElqnhF.s]
        ignore line: [GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4e794.dir\\CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_4e794.exe]
        ignore line: ["C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_4e794.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Users\\<USER>\\New folder (2)\\cmake-4.0.3-windows-x86_64\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_4e794.dir/objects.a]
        ignore line: [C:\\Users\\<USER>\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_4e794.dir/objects.a @CMakeFiles\\cmTC_4e794.dir\\objects1.rsp]
        ignore line: [C:\\Users\\<USER>\\mingw64\\bin\\c++.exe  -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_4e794.dir/objects.a -Wl --no-whole-archive -o cmTC_4e794.exe -Wl --out-implib libcmTC_4e794.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\mingw64\\bin\\c++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: mcf]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (x86_64-mcf-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4e794.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_4e794.']
        link line: [ C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccoNYo7g.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmcfgthread -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lntdll --sysroot=C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_4e794.exe C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/Users/<USER>/mingw64/bin/../lib/gcc -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_4e794.dir/objects.a --no-whole-archive --out-implib libcmTC_4e794.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lmcfgthread -lkernel32 -lntdll C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/Users/<USER>/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccoNYo7g.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmcfgthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lntdll] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-liconv] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmcfgthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lntdll] ==> ignore
          arg [--sysroot=C:/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_4e794.exe] ==> ignore
          arg [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_4e794.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_4e794.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmcfgthread] ==> lib [mcfgthread]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lntdll] ==> lib [ntdll]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmcfgthread] ==> lib [mcfgthread]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lntdll] ==> lib [ntdll]
          arg [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        linker tool for 'CXX': ../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
        collapse obj [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc] ==> [C:/Users/<USER>/mingw64/lib/gcc]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [C:/Users/<USER>/mingw64/lib]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [C:/Users/<USER>/mingw64/lib]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;mingwex;kernel32;mcfgthread;kernel32;ntdll;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc_s;gcc;mingwex;kernel32;mcfgthread;kernel32;ntdll]
        implicit objs: [C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib/crt2.o;C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o;C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        implicit dirs: [C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;C:/Users/<USER>/mingw64/lib/gcc;C:/Users/<USER>/mingw64/x86_64-w64-mingw32/lib;C:/Users/<USER>/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=mcf --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-mcf-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-mcf-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "--version"
      
...
