# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/6_ school/CMakeLists.txt"
  "CMakeFiles/4.0.3/CMakeCCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeRCCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeSystem.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCXXInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeGenericSystem.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeRCInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU-C.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU-CXX.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/GNU.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-C.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-CXX.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-C.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU-C.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-windres.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/SchoolManagementSystem.dir/DependInfo.cmake"
  )
