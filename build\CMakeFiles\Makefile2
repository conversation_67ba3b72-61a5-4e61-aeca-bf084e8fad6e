# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = "C:\6_ school"

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = "C:\6_ school\build"

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/SchoolManagementSystem.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/SchoolManagementSystem.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/SchoolManagementSystem.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/SchoolManagementSystem.dir

# All Build rule for target.
CMakeFiles/SchoolManagementSystem.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir="C:\6_ school\build\CMakeFiles" --progress-num=1,2,3,4,5,6,7 "Built target SchoolManagementSystem"
.PHONY : CMakeFiles/SchoolManagementSystem.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/SchoolManagementSystem.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start "C:\6_ school\build\CMakeFiles" 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/SchoolManagementSystem.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start "C:\6_ school\build\CMakeFiles" 0
.PHONY : CMakeFiles/SchoolManagementSystem.dir/rule

# Convenience name for target.
SchoolManagementSystem: CMakeFiles/SchoolManagementSystem.dir/rule
.PHONY : SchoolManagementSystem

# codegen rule for target.
CMakeFiles/SchoolManagementSystem.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir="C:\6_ school\build\CMakeFiles" --progress-num=1,2,3,4,5,6,7 "Finished codegen for target SchoolManagementSystem"
.PHONY : CMakeFiles/SchoolManagementSystem.dir/codegen

# clean rule for target.
CMakeFiles/SchoolManagementSystem.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/clean
.PHONY : CMakeFiles/SchoolManagementSystem.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

