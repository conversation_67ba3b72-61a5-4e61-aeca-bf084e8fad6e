
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "C:/6_ school/src/course.cpp" "CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj" "gcc" "CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj.d"
  "C:/6_ school/src/grade.cpp" "CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj" "gcc" "CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj.d"
  "C:/6_ school/src/main.cpp" "CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj" "gcc" "CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj.d"
  "C:/6_ school/src/student.cpp" "CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj" "gcc" "CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj.d"
  "C:/6_ school/src/teacher.cpp" "CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj" "gcc" "CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
