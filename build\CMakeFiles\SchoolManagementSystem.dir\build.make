# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = "C:\6_ school"

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = "C:\6_ school\build"

# Include any dependencies generated for this target.
include CMakeFiles/SchoolManagementSystem.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/SchoolManagementSystem.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/SchoolManagementSystem.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/SchoolManagementSystem.dir/flags.make

CMakeFiles/SchoolManagementSystem.dir/codegen:
.PHONY : CMakeFiles/SchoolManagementSystem.dir/codegen

CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/flags.make
CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp
CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj: C:/6_\ school/src/main.cpp
CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="C:\6_ school\build\CMakeFiles" --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj -MF CMakeFiles\SchoolManagementSystem.dir\src\main.cpp.obj.d -o CMakeFiles\SchoolManagementSystem.dir\src\main.cpp.obj -c "C:\6_ school\src\main.cpp"

CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.i"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "C:\6_ school\src\main.cpp" > CMakeFiles\SchoolManagementSystem.dir\src\main.cpp.i

CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.s"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "C:\6_ school\src\main.cpp" -o CMakeFiles\SchoolManagementSystem.dir\src\main.cpp.s

CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/flags.make
CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp
CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj: C:/6_\ school/src/student.cpp
CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="C:\6_ school\build\CMakeFiles" --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj -MF CMakeFiles\SchoolManagementSystem.dir\src\student.cpp.obj.d -o CMakeFiles\SchoolManagementSystem.dir\src\student.cpp.obj -c "C:\6_ school\src\student.cpp"

CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.i"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "C:\6_ school\src\student.cpp" > CMakeFiles\SchoolManagementSystem.dir\src\student.cpp.i

CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.s"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "C:\6_ school\src\student.cpp" -o CMakeFiles\SchoolManagementSystem.dir\src\student.cpp.s

CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/flags.make
CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp
CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj: C:/6_\ school/src/teacher.cpp
CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="C:\6_ school\build\CMakeFiles" --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj -MF CMakeFiles\SchoolManagementSystem.dir\src\teacher.cpp.obj.d -o CMakeFiles\SchoolManagementSystem.dir\src\teacher.cpp.obj -c "C:\6_ school\src\teacher.cpp"

CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.i"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "C:\6_ school\src\teacher.cpp" > CMakeFiles\SchoolManagementSystem.dir\src\teacher.cpp.i

CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.s"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "C:\6_ school\src\teacher.cpp" -o CMakeFiles\SchoolManagementSystem.dir\src\teacher.cpp.s

CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/flags.make
CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp
CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj: C:/6_\ school/src/course.cpp
CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="C:\6_ school\build\CMakeFiles" --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj -MF CMakeFiles\SchoolManagementSystem.dir\src\course.cpp.obj.d -o CMakeFiles\SchoolManagementSystem.dir\src\course.cpp.obj -c "C:\6_ school\src\course.cpp"

CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.i"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "C:\6_ school\src\course.cpp" > CMakeFiles\SchoolManagementSystem.dir\src\course.cpp.i

CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.s"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "C:\6_ school\src\course.cpp" -o CMakeFiles\SchoolManagementSystem.dir\src\course.cpp.s

CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/flags.make
CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp
CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj: C:/6_\ school/src/grade.cpp
CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj: CMakeFiles/SchoolManagementSystem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="C:\6_ school\build\CMakeFiles" --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj -MF CMakeFiles\SchoolManagementSystem.dir\src\grade.cpp.obj.d -o CMakeFiles\SchoolManagementSystem.dir\src\grade.cpp.obj -c "C:\6_ school\src\grade.cpp"

CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.i"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "C:\6_ school\src\grade.cpp" > CMakeFiles\SchoolManagementSystem.dir\src\grade.cpp.i

CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.s"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "C:\6_ school\src\grade.cpp" -o CMakeFiles\SchoolManagementSystem.dir\src\grade.cpp.s

# Object files for target SchoolManagementSystem
SchoolManagementSystem_OBJECTS = \
"CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj" \
"CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj" \
"CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj" \
"CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj" \
"CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj"

# External object files for target SchoolManagementSystem
SchoolManagementSystem_EXTERNAL_OBJECTS =

bin/SchoolManagementSystem.exe: CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj
bin/SchoolManagementSystem.exe: CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj
bin/SchoolManagementSystem.exe: CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj
bin/SchoolManagementSystem.exe: CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj
bin/SchoolManagementSystem.exe: CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj
bin/SchoolManagementSystem.exe: CMakeFiles/SchoolManagementSystem.dir/build.make
bin/SchoolManagementSystem.exe: CMakeFiles/SchoolManagementSystem.dir/linkLibs.rsp
bin/SchoolManagementSystem.exe: CMakeFiles/SchoolManagementSystem.dir/objects1.rsp
bin/SchoolManagementSystem.exe: CMakeFiles/SchoolManagementSystem.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir="C:\6_ school\build\CMakeFiles" --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX executable bin\SchoolManagementSystem.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\SchoolManagementSystem.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/SchoolManagementSystem.dir/build: bin/SchoolManagementSystem.exe
.PHONY : CMakeFiles/SchoolManagementSystem.dir/build

CMakeFiles/SchoolManagementSystem.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\SchoolManagementSystem.dir\cmake_clean.cmake
.PHONY : CMakeFiles/SchoolManagementSystem.dir/clean

CMakeFiles/SchoolManagementSystem.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" "C:\6_ school" "C:\6_ school" "C:\6_ school\build" "C:\6_ school\build" "C:\6_ school\build\CMakeFiles\SchoolManagementSystem.dir\DependInfo.cmake" "--color=$(COLOR)"
.PHONY : CMakeFiles/SchoolManagementSystem.dir/depend

