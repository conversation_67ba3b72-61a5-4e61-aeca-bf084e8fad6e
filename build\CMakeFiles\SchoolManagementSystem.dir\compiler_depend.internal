# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj
 C:/6_ school/src/course.cpp
 C:/6_ school/include/course.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/algorithm
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/binders.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bit
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/algorithmfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/alloc_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/char_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/charconv.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/codecvt.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/concept_check.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cpp_type_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_forced.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_init_exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_ptr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functexcept.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functional_hash.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/hash_bytes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/invoke.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ios_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/istream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_conv.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets_nonio.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets_nonio.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/localefwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memory_resource.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memoryfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/move.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/nested_exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/new_allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream_insert.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/postypes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/predefined_ops.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ptr_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/quoted_string.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/range_access.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/refwrap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/requires_hosted.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/sstream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/std_abs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algo.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algobase.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_bvector.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_construct.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_function.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_heap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_funcs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_types.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_pair.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_tempbuf.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_uninitialized.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_vector.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf_iterator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/string_view.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stringfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uniform_int_dist.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator_args.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/utility.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/vector.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/version.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cctype
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cerrno
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/clocale
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/concepts
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstddef
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdio
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdlib
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ctime
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwchar
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwctype
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/assertions.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/debug.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/exception
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/alloc_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/atomicity.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/numeric_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/string_conversions.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/type_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/initializer_list
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iomanip
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ios
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iosfwd
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iostream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/istream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/locale
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/new
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ostream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/execution_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/glue_algorithm_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/pstl_config.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/sstream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdexcept
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdlib.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/streambuf
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string_view
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/system_error
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/tuple
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/type_traits
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/typeinfo
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/vector
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/limits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mm_malloc.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdbool.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stddef.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdint.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/syslimits.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_mac.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_off_t.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_secapi.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_stat64.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_timeval.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/crtdefs.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/ctype.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/errno.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/limits.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/locale.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/malloc.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/atomic.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/cond.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/dtor_queue.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/fwd.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/gthr.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/gthr_aux.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/mutex.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/once.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/shared_mutex.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/thread.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/tls.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/version.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/pthread_time.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/stdlib_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stddef.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdint.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdio.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdlib.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/swprintf.inl
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sys/timeb.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/time.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/vadefs.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/wchar.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/wctype.h

CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj
 C:/6_ school/src/grade.cpp
 C:/6_ school/include/grade.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/algorithm
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/binders.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bit
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/algorithmfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/alloc_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/char_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/charconv.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/codecvt.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/concept_check.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cpp_type_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_forced.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_init_exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/erase_if.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_ptr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functexcept.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functional_hash.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/hash_bytes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/invoke.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ios_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/istream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_conv.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets_nonio.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets_nonio.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/localefwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memory_resource.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memoryfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/move.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/nested_exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/new_allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/node_handle.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream_insert.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/postypes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/predefined_ops.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ptr_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/quoted_string.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/range_access.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/refwrap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/requires_hosted.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/sstream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/std_abs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algo.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algobase.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_bvector.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_construct.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_function.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_heap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_funcs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_types.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_map.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_multimap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_pair.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_tempbuf.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_tree.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_uninitialized.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_vector.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf_iterator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/string_view.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stringfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uniform_int_dist.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator_args.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/utility.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/vector.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/version.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cctype
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cerrno
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/clocale
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/concepts
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstddef
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdio
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdlib
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ctime
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwchar
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwctype
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/assertions.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/debug.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/exception
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/aligned_buffer.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/alloc_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/atomicity.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/numeric_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/string_conversions.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/type_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/initializer_list
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iomanip
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ios
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iosfwd
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iostream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/istream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/locale
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/map
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/new
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ostream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/execution_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/glue_algorithm_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/pstl_config.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/sstream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdexcept
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdlib.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/streambuf
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string_view
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/system_error
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/tuple
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/type_traits
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/typeinfo
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/vector
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/limits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mm_malloc.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdbool.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stddef.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdint.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/syslimits.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_mac.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_off_t.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_secapi.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_stat64.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_timeval.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/crtdefs.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/ctype.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/errno.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/limits.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/locale.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/malloc.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/atomic.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/cond.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/dtor_queue.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/fwd.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/gthr.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/gthr_aux.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/mutex.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/once.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/shared_mutex.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/thread.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/tls.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/version.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/pthread_time.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/stdlib_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stddef.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdint.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdio.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdlib.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/swprintf.inl
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sys/timeb.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/time.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/vadefs.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/wchar.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/wctype.h

CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj
 C:/6_ school/src/main.cpp
 C:/6_ school/include/course.h
 C:/6_ school/include/database.h
 C:/6_ school/include/grade.h
 C:/6_ school/include/sqlite3.h
 C:/6_ school/include/student.h
 C:/6_ school/include/teacher.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/algorithm
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/auto_ptr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/binders.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bit
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/algorithmfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/align.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/alloc_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/allocated_ptr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/atomic_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/atomic_lockfree_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/char_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/charconv.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/codecvt.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/concept_check.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cpp_type_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_forced.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_init_exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/erase_if.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_ptr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functexcept.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functional_hash.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/hash_bytes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/invoke.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ios_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/istream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_conv.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets_nonio.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets_nonio.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/localefwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memory_resource.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memoryfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/move.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/nested_exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/new_allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/node_handle.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream_insert.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/postypes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/predefined_ops.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ptr_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/quoted_string.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/range_access.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/refwrap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/requires_hosted.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/shared_ptr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/shared_ptr_atomic.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/shared_ptr_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/sstream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/std_abs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algo.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algobase.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_bvector.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_construct.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_function.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_heap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_funcs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_types.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_map.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_multimap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_pair.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_raw_storage_iter.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_tempbuf.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_tree.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_uninitialized.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_vector.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf_iterator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/string_view.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stringfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uniform_int_dist.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/unique_ptr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator_args.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/utility.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/vector.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/version.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cctype
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cerrno
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/clocale
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/concepts
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstddef
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdio
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdlib
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ctime
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwchar
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwctype
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/assertions.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/debug.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/exception
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/aligned_buffer.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/alloc_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/atomicity.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/concurrence.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/numeric_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/string_conversions.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/type_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/initializer_list
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iomanip
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ios
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iosfwd
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iostream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/istream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/limits
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/locale
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/map
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/memory
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/new
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ostream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/execution_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/glue_algorithm_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/glue_memory_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/pstl_config.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/sstream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdexcept
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdlib.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/streambuf
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string_view
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/system_error
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/tuple
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/type_traits
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/typeinfo
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/vector
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/limits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mm_malloc.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdbool.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stddef.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdint.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/syslimits.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_mac.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_off_t.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_secapi.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_stat64.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_timeval.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/crtdefs.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/ctype.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/errno.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/limits.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/locale.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/malloc.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/atomic.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/cond.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/dtor_queue.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/fwd.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/gthr.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/gthr_aux.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/mutex.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/once.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/shared_mutex.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/thread.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/tls.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/version.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/pthread_time.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/stdlib_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stddef.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdint.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdio.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdlib.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/swprintf.inl
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sys/timeb.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/time.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/vadefs.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/wchar.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/wctype.h

CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj
 C:/6_ school/src/student.cpp
 C:/6_ school/include/student.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/algorithm
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/binders.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bit
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/algorithmfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/alloc_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/char_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/charconv.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/codecvt.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/concept_check.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cpp_type_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_forced.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_init_exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_ptr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functexcept.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functional_hash.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/hash_bytes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/invoke.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ios_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/istream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_conv.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets_nonio.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets_nonio.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/localefwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memory_resource.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memoryfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/move.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/nested_exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/new_allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream_insert.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/postypes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/predefined_ops.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ptr_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/quoted_string.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/range_access.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/refwrap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/requires_hosted.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/sstream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/std_abs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algo.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algobase.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_bvector.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_construct.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_function.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_heap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_funcs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_types.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_pair.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_tempbuf.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_uninitialized.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_vector.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf_iterator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/string_view.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stringfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uniform_int_dist.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator_args.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/utility.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/vector.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/version.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cctype
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cerrno
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/clocale
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/concepts
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstddef
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdio
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdlib
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ctime
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwchar
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwctype
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/assertions.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/debug.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/exception
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/alloc_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/atomicity.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/numeric_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/string_conversions.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/type_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/initializer_list
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iomanip
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ios
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iosfwd
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iostream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/istream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/locale
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/new
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ostream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/execution_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/glue_algorithm_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/pstl_config.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/sstream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdexcept
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdlib.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/streambuf
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string_view
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/system_error
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/tuple
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/type_traits
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/typeinfo
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/vector
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/limits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mm_malloc.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdbool.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stddef.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdint.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/syslimits.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_mac.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_off_t.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_secapi.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_stat64.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_timeval.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/crtdefs.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/ctype.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/errno.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/limits.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/locale.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/malloc.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/atomic.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/cond.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/dtor_queue.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/fwd.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/gthr.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/gthr_aux.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/mutex.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/once.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/shared_mutex.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/thread.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/tls.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/version.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/pthread_time.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/stdlib_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stddef.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdint.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdio.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdlib.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/swprintf.inl
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sys/timeb.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/time.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/vadefs.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/wchar.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/wctype.h

CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj
 C:/6_ school/src/teacher.cpp
 C:/6_ school/include/teacher.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/algorithm
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/binders.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bit
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/algorithmfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/alloc_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/char_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/charconv.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/codecvt.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/concept_check.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cpp_type_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_forced.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_init_exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_ptr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functexcept.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functional_hash.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/hash_bytes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/invoke.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ios_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/istream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_conv.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets_nonio.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets_nonio.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/localefwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memory_resource.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memoryfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/move.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/nested_exception.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/new_allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream_insert.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/postypes.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/predefined_ops.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ptr_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/quoted_string.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/range_access.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/refwrap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/requires_hosted.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/sstream.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/std_abs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algo.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algobase.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_bvector.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_construct.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_function.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_heap.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_funcs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_types.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_pair.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_tempbuf.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_uninitialized.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_vector.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf_iterator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/string_view.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stringfwd.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uniform_int_dist.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator_args.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/utility.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/vector.tcc
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/version.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cctype
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cerrno
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/clocale
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/concepts
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstddef
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdio
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdlib
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ctime
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwchar
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwctype
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/assertions.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/debug.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/exception
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/alloc_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/atomicity.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/numeric_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/string_conversions.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/type_traits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/initializer_list
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iomanip
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ios
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iosfwd
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iostream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/istream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/locale
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/new
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ostream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/execution_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/glue_algorithm_defs.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/pstl_config.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/sstream
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdexcept
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdlib.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/streambuf
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string_view
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/system_error
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/tuple
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/type_traits
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/typeinfo
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/vector
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/limits.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mm_malloc.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdbool.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stddef.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdint.h
 C:/Users/<USER>/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/syslimits.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_mac.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_off_t.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_secapi.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_mingw_stat64.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/_timeval.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/crtdefs.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/ctype.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/errno.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/limits.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/locale.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/malloc.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/atomic.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/cond.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/dtor_queue.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/fwd.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/gthr.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/gthr_aux.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/mutex.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/once.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/shared_mutex.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/thread.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/tls.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/mcfgthread/version.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/pthread_time.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/stdlib_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stddef.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdint.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdio.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/stdlib.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/swprintf.inl
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/sys/timeb.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/time.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/vadefs.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/wchar.h
 C:/Users/<USER>/mingw64/x86_64-w64-mingw32/include/wctype.h

