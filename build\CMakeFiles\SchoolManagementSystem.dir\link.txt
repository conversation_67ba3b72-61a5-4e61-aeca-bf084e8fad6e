"C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -E rm -f CMakeFiles\SchoolManagementSystem.dir/objects.a
C:\Users\<USER>\mingw64\bin\ar.exe qc CMakeFiles\SchoolManagementSystem.dir/objects.a @CMakeFiles\SchoolManagementSystem.dir\objects1.rsp
C:\Users\<USER>\mingw64\bin\c++.exe -Wl,--whole-archive CMakeFiles\SchoolManagementSystem.dir/objects.a -Wl,--no-whole-archive -o bin\SchoolManagementSystem.exe -Wl,--out-implib,libSchoolManagementSystem.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\SchoolManagementSystem.dir\linkLibs.rsp
