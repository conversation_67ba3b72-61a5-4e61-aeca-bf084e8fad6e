# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = "C:\6_ school"

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = "C:\6_ school\build"

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	"C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	"C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	"C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	"C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	"C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	"C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	"C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	"C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start "C:\6_ school\build\CMakeFiles" "C:\6_ school\build\\CMakeFiles\progress.marks"
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start "C:\6_ school\build\CMakeFiles" 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named SchoolManagementSystem

# Build rule for target.
SchoolManagementSystem: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 SchoolManagementSystem
.PHONY : SchoolManagementSystem

# fast build rule for target.
SchoolManagementSystem/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/build
.PHONY : SchoolManagementSystem/fast

src/course.obj: src/course.cpp.obj
.PHONY : src/course.obj

# target to build an object file
src/course.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj
.PHONY : src/course.cpp.obj

src/course.i: src/course.cpp.i
.PHONY : src/course.i

# target to preprocess a source file
src/course.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.i
.PHONY : src/course.cpp.i

src/course.s: src/course.cpp.s
.PHONY : src/course.s

# target to generate assembly for a file
src/course.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.s
.PHONY : src/course.cpp.s

src/grade.obj: src/grade.cpp.obj
.PHONY : src/grade.obj

# target to build an object file
src/grade.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj
.PHONY : src/grade.cpp.obj

src/grade.i: src/grade.cpp.i
.PHONY : src/grade.i

# target to preprocess a source file
src/grade.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.i
.PHONY : src/grade.cpp.i

src/grade.s: src/grade.cpp.s
.PHONY : src/grade.s

# target to generate assembly for a file
src/grade.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.s
.PHONY : src/grade.cpp.s

src/main.obj: src/main.cpp.obj
.PHONY : src/main.obj

# target to build an object file
src/main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj
.PHONY : src/main.cpp.obj

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/student.obj: src/student.cpp.obj
.PHONY : src/student.obj

# target to build an object file
src/student.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj
.PHONY : src/student.cpp.obj

src/student.i: src/student.cpp.i
.PHONY : src/student.i

# target to preprocess a source file
src/student.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.i
.PHONY : src/student.cpp.i

src/student.s: src/student.cpp.s
.PHONY : src/student.s

# target to generate assembly for a file
src/student.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.s
.PHONY : src/student.cpp.s

src/teacher.obj: src/teacher.cpp.obj
.PHONY : src/teacher.obj

# target to build an object file
src/teacher.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj
.PHONY : src/teacher.cpp.obj

src/teacher.i: src/teacher.cpp.i
.PHONY : src/teacher.i

# target to preprocess a source file
src/teacher.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.i
.PHONY : src/teacher.cpp.i

src/teacher.s: src/teacher.cpp.s
.PHONY : src/teacher.s

# target to generate assembly for a file
src/teacher.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\SchoolManagementSystem.dir\build.make CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.s
.PHONY : src/teacher.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... SchoolManagementSystem
	@echo ... src/course.obj
	@echo ... src/course.i
	@echo ... src/course.s
	@echo ... src/grade.obj
	@echo ... src/grade.i
	@echo ... src/grade.s
	@echo ... src/main.obj
	@echo ... src/main.i
	@echo ... src/main.s
	@echo ... src/student.obj
	@echo ... src/student.i
	@echo ... src/student.s
	@echo ... src/teacher.obj
	@echo ... src/teacher.i
	@echo ... src/teacher.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

