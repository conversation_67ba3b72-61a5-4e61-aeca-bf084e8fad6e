[{"directory": "C:/6_ school/build", "command": "C:\\Users\\<USER>\\mingw64\\bin\\c++.exe  @CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -Wpedantic -o CMakeFiles\\SchoolManagementSystem.dir\\src\\main.cpp.obj -c \"C:\\6_ school\\src\\main.cpp\"", "file": "C:/6_ school/src/main.cpp", "output": "CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj"}, {"directory": "C:/6_ school/build", "command": "C:\\Users\\<USER>\\mingw64\\bin\\c++.exe  @CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -Wpedantic -o CMakeFiles\\SchoolManagementSystem.dir\\src\\student.cpp.obj -c \"C:\\6_ school\\src\\student.cpp\"", "file": "C:/6_ school/src/student.cpp", "output": "CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj"}, {"directory": "C:/6_ school/build", "command": "C:\\Users\\<USER>\\mingw64\\bin\\c++.exe  @CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -Wpedantic -o CMakeFiles\\SchoolManagementSystem.dir\\src\\teacher.cpp.obj -c \"C:\\6_ school\\src\\teacher.cpp\"", "file": "C:/6_ school/src/teacher.cpp", "output": "CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj"}, {"directory": "C:/6_ school/build", "command": "C:\\Users\\<USER>\\mingw64\\bin\\c++.exe  @CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -Wpedantic -o CMakeFiles\\SchoolManagementSystem.dir\\src\\course.cpp.obj -c \"C:\\6_ school\\src\\course.cpp\"", "file": "C:/6_ school/src/course.cpp", "output": "CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj"}, {"directory": "C:/6_ school/build", "command": "C:\\Users\\<USER>\\mingw64\\bin\\c++.exe  @CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -Wpedantic -o CMakeFiles\\SchoolManagementSystem.dir\\src\\grade.cpp.obj -c \"C:\\6_ school\\src\\grade.cpp\"", "file": "C:/6_ school/src/grade.cpp", "output": "CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj"}, {"directory": "C:/6_ school/build", "command": "C:\\Users\\<USER>\\mingw64\\bin\\c++.exe  @CMakeFiles/SchoolManagementSystem.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -Wpedantic -o CMakeFiles\\SchoolManagementSystem.dir\\src\\database.cpp.obj -c \"C:\\6_ school\\src\\database.cpp\"", "file": "C:/6_ school/src/database.cpp", "output": "CMakeFiles/SchoolManagementSystem.dir/src/database.cpp.obj"}]