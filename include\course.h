#ifndef COURSE_H
#define COURSE_H

#include <string>
#include <vector>
#include <iostream>

class Course {
private:
    std::string courseId;
    std::string courseName;
    std::string description;
    int credits;
    std::string teacherId;
    std::vector<std::string> enrolledStudents;
    int maxCapacity;
    std::string schedule;

public:
    // Constructors
    Course();
    Course(const std::string& courseId, const std::string& courseName,
           int credits, int maxCapacity);

    // Destructor
    ~Course() = default;

    // Getters
    std::string getCourseId() const { return courseId; }
    std::string getCourseName() const { return courseName; }
    std::string getDescription() const { return description; }
    int getCredits() const { return credits; }
    std::string getTeacherId() const { return teacherId; }
    std::vector<std::string> getEnrolledStudents() const { return enrolledStudents; }
    int getMaxCapacity() const { return maxCapacity; }
    int getEnrolledCount() const { return static_cast<int>(enrolledStudents.size()); }
    int getAvailableSlots() const { return maxCapacity - getEnrolledCount(); }
    std::string getSchedule() const { return schedule; }

    // Setters
    void setCourseName(const std::string& courseName) { this->courseName = courseName; }
    void setDescription(const std::string& description) { this->description = description; }
    void setCredits(int credits) { this->credits = credits; }
    void setTeacherId(const std::string& teacherId) { this->teacherId = teacherId; }
    void setMaxCapacity(int maxCapacity) { this->maxCapacity = maxCapacity; }
    void setSchedule(const std::string& schedule) { this->schedule = schedule; }

    // Student enrollment management
    bool enrollStudent(const std::string& studentId);
    bool removeStudent(const std::string& studentId);
    bool isStudentEnrolled(const std::string& studentId) const;
    bool isFull() const { return getEnrolledCount() >= maxCapacity; }

    // Utility functions
    void displayInfo() const;
    void displayEnrolledStudents() const;
    bool isValid() const;

    // Operators
    bool operator==(const Course& other) const;
    friend std::ostream& operator<<(std::ostream& os, const Course& course);
};

#endif // COURSE_H
