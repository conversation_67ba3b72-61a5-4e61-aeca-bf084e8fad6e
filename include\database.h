#ifndef DATABASE_H
#define DATABASE_H

#include <sqlite3.h>
#include <string>
#include <vector>
#include <memory>
#include "student.h"
#include "teacher.h"
#include "course.h"
#include "grade.h"

class Database {
private:
    sqlite3* db;
    std::string dbPath;
    
    // Helper methods
    bool executeSQL(const std::string& sql);
    sqlite3_stmt* prepareStatement(const std::string& sql);
    void finalizeStatement(sqlite3_stmt* stmt);
    
public:
    Database(const std::string& dbPath = "school_management.db");
    ~Database();
    
    // Database initialization
    bool initialize();
    bool createTables();
    void close();
    
    // Student operations
    bool insertStudent(const Student& student);
    bool updateStudent(const Student& student);
    bool deleteStudent(const std::string& studentId);
    Student getStudent(const std::string& studentId);
    std::vector<Student> getAllStudents();
    std::vector<Student> searchStudents(const std::string& searchTerm);
    
    // Teacher operations
    bool insertTeacher(const Teacher& teacher);
    bool updateTeacher(const Teacher& teacher);
    bool deleteTeacher(const std::string& teacherId);
    Teacher getTeacher(const std::string& teacherId);
    std::vector<Teacher> getAllTeachers();
    std::vector<Teacher> searchTeachers(const std::string& searchTerm);
    
    // Course operations
    bool insertCourse(const Course& course);
    bool updateCourse(const Course& course);
    bool deleteCourse(const std::string& courseId);
    Course getCourse(const std::string& courseId);
    std::vector<Course> getAllCourses();
    std::vector<Course> searchCourses(const std::string& searchTerm);
    
    // Grade operations
    bool insertGrade(const Grade& grade);
    bool updateGrade(const Grade& grade);
    bool deleteGrade(const std::string& studentId, const std::string& courseId, const std::string& gradeType);
    std::vector<Grade> getStudentGrades(const std::string& studentId);
    std::vector<Grade> getCourseGrades(const std::string& courseId);
    std::vector<Grade> getAllGrades();
    
    // Enrollment operations
    bool enrollStudentInCourse(const std::string& studentId, const std::string& courseId);
    bool dropStudentFromCourse(const std::string& studentId, const std::string& courseId);
    std::vector<std::string> getStudentCourses(const std::string& studentId);
    std::vector<std::string> getCourseStudents(const std::string& courseId);
    
    // Teacher assignment operations
    bool assignTeacherToCourse(const std::string& teacherId, const std::string& courseId);
    bool unassignTeacherFromCourse(const std::string& teacherId, const std::string& courseId);
    std::vector<std::string> getTeacherCourses(const std::string& teacherId);
    
    // Statistics and reports
    int getStudentCount();
    int getTeacherCount();
    int getCourseCount();
    int getGradeCount();
    double getStudentGPA(const std::string& studentId);
    double getCourseAverageGrade(const std::string& courseId);
    
    // Test data generation
    bool generateTestData();
    bool clearAllData();
    
    // Utility methods
    bool isConnected() const;
    std::string getLastError() const;
};

#endif // DATABASE_H
