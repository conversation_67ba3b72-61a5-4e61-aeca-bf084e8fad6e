#ifndef GRADE_H
#define GRADE_H

#include <string>
#include <vector>
#include <iostream>
#include <map>

class Grade {
private:
    std::string studentId;
    std::string courseId;
    double score;
    std::string gradeType; // "Quiz", "Midterm", "Final", "Assignment"
    double weight;
    std::string dateRecorded;

public:
    // Constructors
    Grade();
    Grade(const std::string& studentId, const std::string& courseId,
          double score, const std::string& gradeType, double weight = 1.0);

    // Destructor
    ~Grade() = default;

    // Getters
    std::string getStudentId() const { return studentId; }
    std::string getCourseId() const { return courseId; }
    double getScore() const { return score; }
    std::string getGradeType() const { return gradeType; }
    double getWeight() const { return weight; }
    std::string getDateRecorded() const { return dateRecorded; }

    // Setters
    void setScore(double score) { this->score = score; }
    void setGradeType(const std::string& gradeType) { this->gradeType = gradeType; }
    void setWeight(double weight) { this->weight = weight; }
    void setDateRecorded(const std::string& date) { this->dateRecorded = date; }

    // Grade calculation functions
    char getLetterGrade() const;
    double getGradePoints() const;
    bool isPassingGrade() const;

    // Utility functions
    void displayInfo() const;
    bool isValid() const;

    // Static utility functions
    static char convertToLetterGrade(double score);
    static double convertToGradePoints(char letterGrade);
    static bool isValidScore(double score);

    // Operators
    bool operator==(const Grade& other) const;
    friend std::ostream& operator<<(std::ostream& os, const Grade& grade);
};

// Grade Manager class for handling multiple grades
class GradeManager {
private:
    std::vector<Grade> grades;

public:
    // Grade management
    bool addGrade(const Grade& grade);
    bool updateGrade(const std::string& studentId, const std::string& courseId,
                    const std::string& gradeType, double newScore);
    bool removeGrade(const std::string& studentId, const std::string& courseId,
                    const std::string& gradeType);

    // Grade retrieval
    std::vector<Grade> getStudentGrades(const std::string& studentId) const;
    std::vector<Grade> getCourseGrades(const std::string& courseId) const;
    Grade* findGrade(const std::string& studentId, const std::string& courseId,
                     const std::string& gradeType);

    // GPA calculations
    double calculateCourseGPA(const std::string& studentId, const std::string& courseId) const;
    double calculateOverallGPA(const std::string& studentId) const;

    // Statistics
    double getCourseAverage(const std::string& courseId) const;
    std::map<char, int> getGradeDistribution(const std::string& courseId) const;

    // Utility functions
    void displayAllGrades() const;
    void displayStudentGrades(const std::string& studentId) const;
    void displayCourseGrades(const std::string& courseId) const;
    int getGradeCount() const { return static_cast<int>(grades.size()); }
};

#endif // GRADE_H
