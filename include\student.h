#ifndef STUDENT_H
#define STUDENT_H

#include <string>
#include <vector>
#include <iostream>

class Student {
private:
    std::string id;
    std::string firstName;
    std::string lastName;
    int gradeLevel;
    std::string studentClass;
    std::string email;
    std::string phoneNumber;
    std::vector<std::string> enrolledCourses;
    double gpa;

public:
    // Constructors
    Student();
    Student(const std::string& id, const std::string& firstName, 
            const std::string& lastName, int gradeLevel, 
            const std::string& studentClass = "");

    // Destructor
    ~Student() = default;

    // Getters
    std::string getId() const { return id; }
    std::string getFirstName() const { return firstName; }
    std::string getLastName() const { return lastName; }
    std::string getFullName() const { return firstName + " " + lastName; }
    int getGradeLevel() const { return gradeLevel; }
    std::string getStudentClass() const { return studentClass; }
    std::string getEmail() const { return email; }
    std::string getPhoneNumber() const { return phoneNumber; }
    double getGPA() const { return gpa; }
    std::vector<std::string> getEnrolledCourses() const { return enrolledCourses; }

    // Setters
    void setFirstName(const std::string& firstName) { this->firstName = firstName; }
    void setLastName(const std::string& lastName) { this->lastName = lastName; }
    void setGradeLevel(int gradeLevel) { this->gradeLevel = gradeLevel; }
    void setStudentClass(const std::string& studentClass) { this->studentClass = studentClass; }
    void setEmail(const std::string& email) { this->email = email; }
    void setPhoneNumber(const std::string& phoneNumber) { this->phoneNumber = phoneNumber; }
    void setGPA(double gpa) { this->gpa = gpa; }

    // Course management
    bool enrollInCourse(const std::string& courseId);
    bool dropCourse(const std::string& courseId);
    bool isEnrolledInCourse(const std::string& courseId) const;

    // Utility functions
    void displayInfo() const;
    bool isValid() const;

    // Operators
    bool operator==(const Student& other) const;
    friend std::ostream& operator<<(std::ostream& os, const Student& student);
};

#endif // STUDENT_H
