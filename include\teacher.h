#ifndef TEACHER_H
#define TEACHER_H

#include <string>
#include <vector>
#include <iostream>

class Teacher {
private:
    std::string id;
    std::string firstName;
    std::string lastName;
    std::string department;
    std::string email;
    std::string phoneNumber;
    std::vector<std::string> subjects;
    std::vector<std::string> assignedCourses;
    double salary;

public:
    // Constructors
    Teacher();
    Teacher(const std::string& id, const std::string& firstName,
            const std::string& lastName, const std::string& department);

    // Destructor
    ~Teacher() = default;

    // Getters
    std::string getId() const { return id; }
    std::string getFirstName() const { return firstName; }
    std::string getLastName() const { return lastName; }
    std::string getFullName() const { return firstName + " " + lastName; }
    std::string getDepartment() const { return department; }
    std::string getEmail() const { return email; }
    std::string getPhoneNumber() const { return phoneNumber; }
    std::vector<std::string> getSubjects() const { return subjects; }
    std::vector<std::string> getAssignedCourses() const { return assignedCourses; }
    double getSalary() const { return salary; }

    // Setters
    void setFirstName(const std::string& firstName) { this->firstName = firstName; }
    void setLastName(const std::string& lastName) { this->lastName = lastName; }
    void setDepartment(const std::string& department) { this->department = department; }
    void setEmail(const std::string& email) { this->email = email; }
    void setPhoneNumber(const std::string& phoneNumber) { this->phoneNumber = phoneNumber; }
    void setSalary(double salary) { this->salary = salary; }

    // Subject management
    bool addSubject(const std::string& subject);
    bool removeSubject(const std::string& subject);
    bool hasSubject(const std::string& subject) const;

    // Course management
    bool assignCourse(const std::string& courseId);
    bool removeCourseAssignment(const std::string& courseId);
    bool isAssignedToCourse(const std::string& courseId) const;

    // Utility functions
    void displayInfo() const;
    bool isValid() const;

    // Operators
    bool operator==(const Teacher& other) const;
    friend std::ostream& operator<<(std::ostream& os, const Teacher& teacher);
};

#endif // TEACHER_H
