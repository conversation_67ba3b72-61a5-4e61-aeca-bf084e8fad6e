# School Management System - Project Documentation

## Project Overview

### Purpose
This project is a comprehensive School Management System designed to handle the core administrative functions of an educational institution. The system provides efficient management of students, teachers, courses, and grades through a command-line interface.

### Main Objectives
- **Student Management**: Add, update, delete, and search student records
- **Teacher Management**: Manage teacher information and assignments
- **Course Management**: Handle course creation, scheduling, and enrollment
- **Grade Management**: Record, update, and calculate student grades
- **Error Handling**: Robust error handling for duplicate entries, invalid data, and system constraints

### Key Features
- Interactive command-line interface
- Data validation and error handling
- Student enrollment and course registration
- Grade calculation and reporting
- Teacher-course assignments
- Comprehensive search and filtering capabilities

## Project Structure

### Source Files

#### Core Application Files
- **main.cpp**: Main application entry point with menu system and data management
- **student.h/cpp**: Student class definition and implementation
- **teacher.h/cpp**: Teacher class definition and implementation  
- **course.h/cpp**: Course class definition and implementation
- **grade.h/cpp**: Grade management and calculation system

#### Build Configuration
- **CMakeLists.txt**: CMake build configuration
- **CMakePresets.json**: CMake preset configurations for different build types

### File Descriptions

#### main.cpp
- Contains the main menu system
- Handles user input and navigation
- Manages data persistence and file I/O
- Coordinates between different system components
- Implements error handling for user operations

#### student.h/cpp
- Student class with properties: ID, name, age, grade level, contact information
- Methods for student data manipulation
- Validation for student information
- Search and filter functionality

#### teacher.h/cpp
- Teacher class with properties: ID, name, subject specialization, contact details
- Methods for teacher management
- Course assignment functionality
- Teacher performance tracking

#### course.h/cpp
- Course class with properties: course ID, name, description, credits, schedule
- Student enrollment management
- Teacher assignment to courses
- Prerequisite handling

#### grade.h/cpp
- Grade recording and management
- GPA calculation algorithms
- Grade reporting and transcripts
- Statistical analysis of student performance

## Build System Configuration

### CMakeLists.txt Structure
```cmake
cmake_minimum_required(VERSION 3.20)
project(SchoolManagementSystem)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Source files
set(SOURCES
    src/main.cpp
    src/student.cpp
    src/teacher.cpp
    src/course.cpp
    src/grade.cpp
)

# Header files
set(HEADERS
    include/student.h
    include/teacher.h
    include/course.h
    include/grade.h
)

# Include directories
include_directories(include)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Compiler flags
target_compile_options(${PROJECT_NAME} PRIVATE
    $<$<CXX_COMPILER_ID:MSVC>:/W4>
    $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-Wall -Wextra -Wpedantic>
)
```

### CMakePresets.json Configuration
```json
{
    "version": 3,
    "configurePresets": [
        {
            "name": "default",
            "displayName": "Default Config",
            "description": "Default build configuration",
            "generator": "Ninja",
            "binaryDir": "${sourceDir}/build/${presetName}",
            "cacheVariables": {
                "CMAKE_BUILD_TYPE": "Debug"
            }
        },
        {
            "name": "release",
            "displayName": "Release Config", 
            "description": "Release build configuration",
            "generator": "Ninja",
            "binaryDir": "${sourceDir}/build/${presetName}",
            "cacheVariables": {
                "CMAKE_BUILD_TYPE": "Release"
            }
        }
    ],
    "buildPresets": [
        {
            "name": "default",
            "configurePreset": "default"
        },
        {
            "name": "release",
            "configurePreset": "release"
        }
    ]
}
```

## Practical Examples

### Adding a New Student
```cpp
// Example: Adding a student with error handling
try {
    Student newStudent("S001", "John Smith", 16, 10, "<EMAIL>");
    if (studentManager.addStudent(newStudent)) {
        cout << "Student added successfully!" << endl;
    } else {
        cout << "Error: Student with ID S001 already exists!" << endl;
    }
} catch (const invalid_argument& e) {
    cout << "Invalid student data: " << e.what() << endl;
}
```

### Recording a Grade
```cpp
// Example: Recording a grade for a student
try {
    Grade newGrade("S001", "MATH101", 85.5, "Midterm Exam");
    if (gradeManager.recordGrade(newGrade)) {
        cout << "Grade recorded successfully!" << endl;
        cout << "Student GPA updated: " << gradeManager.calculateGPA("S001") << endl;
    }
} catch (const runtime_error& e) {
    cout << "Error recording grade: " << e.what() << endl;
}
```

### Generating Student Report
```cpp
// Example: Generating comprehensive student report
try {
    StudentReport report = reportManager.generateStudentReport("S001");
    cout << "=== Student Report ===" << endl;
    cout << "Name: " << report.studentName << endl;
    cout << "GPA: " << report.gpa << endl;
    cout << "Enrolled Courses: " << report.enrolledCourses.size() << endl;
    
    for (const auto& course : report.enrolledCourses) {
        cout << "- " << course.courseName << " (Grade: " << course.grade << ")" << endl;
    }
} catch (const runtime_error& e) {
    cout << "Error generating report: " << e.what() << endl;
}
```

### Error Handling Examples
```cpp
// Duplicate student handling
if (studentExists(studentId)) {
    throw runtime_error("Student with ID " + studentId + " already exists");
}

// Invalid grade validation
if (grade < 0 || grade > 100) {
    throw invalid_argument("Grade must be between 0 and 100");
}

// Course capacity check
if (course.getEnrolledCount() >= course.getMaxCapacity()) {
    throw runtime_error("Course is at maximum capacity");
}
```

## Future Development

### Planned Enhancements
1. **Graphical User Interface**
   - Qt or GTK-based GUI implementation
   - Modern web-based interface using REST API
   - Mobile application support

2. **Database Integration**
   - SQLite for local storage
   - MySQL/PostgreSQL for enterprise deployment
   - Data migration and backup utilities

3. **Advanced Reporting System**
   - PDF report generation
   - Statistical analysis and charts
   - Performance tracking over time
   - Automated report scheduling

4. **Additional Features**
   - Attendance tracking system
   - Library management integration
   - Fee management and billing
   - Parent portal and communication system
   - Timetable and scheduling management

### Technical Improvements
- Multi-threading for better performance
- Network capabilities for distributed systems
- Enhanced security and user authentication
- Configuration file support
- Logging and audit trail functionality

## Troubleshooting

### Common Issues and Solutions

#### Build Issues
- **CMake not found**: Ensure CMake 3.20+ is installed and in PATH
- **Compiler errors**: Verify C++17 compatible compiler (GCC 7+, Clang 5+, MSVC 2017+)
- **Missing headers**: Check include directory structure matches CMakeLists.txt

#### Runtime Issues
- **File I/O errors**: Verify read/write permissions in application directory
- **Memory issues**: Check for proper object lifecycle management
- **Data corruption**: Implement data validation and backup mechanisms

#### Data Management Issues
- **Duplicate entries**: Implement unique ID validation before insertion
- **Invalid references**: Check foreign key constraints (student-course relationships)
- **Data inconsistency**: Implement transaction-like operations for related data updates

### Debug Configuration
```cmake
# Add debug symbols and disable optimizations
set(CMAKE_BUILD_TYPE Debug)
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")

# Enable additional warnings
target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra -Werror)
```

## Getting Started

### Prerequisites
- CMake 3.20 or higher
- C++17 compatible compiler
- Git (for version control)

### Build Instructions
1. Clone the repository
2. Create build directory: `mkdir build && cd build`
3. Configure: `cmake --preset=default ..`
4. Build: `cmake --build . --preset=default`
5. Run: `./SchoolManagementSystem`

### Usage
1. Launch the application
2. Use the main menu to navigate between modules
3. Follow on-screen prompts for data entry
4. Use help commands for detailed usage information

## System Architecture

### Class Hierarchy and Relationships

#### Person Base Class
```cpp
class Person {
protected:
    string id;
    string firstName;
    string lastName;
    string email;
    string phoneNumber;
    Date dateOfBirth;

public:
    Person(const string& id, const string& firstName, const string& lastName);
    virtual ~Person() = default;

    // Getters and setters
    string getId() const { return id; }
    string getFullName() const { return firstName + " " + lastName; }
    virtual string getRole() const = 0;
};
```

#### Student Class (Inherits from Person)
```cpp
class Student : public Person {
private:
    int gradeLevel;
    string studentClass;
    Date enrollmentDate;
    vector<string> enrolledCourses;
    double gpa;

public:
    Student(const string& id, const string& firstName, const string& lastName,
            int gradeLevel, const string& studentClass);

    string getRole() const override { return "Student"; }
    void enrollInCourse(const string& courseId);
    void dropCourse(const string& courseId);
    double calculateGPA() const;
    vector<Grade> getGrades() const;
};
```

#### Teacher Class (Inherits from Person)
```cpp
class Teacher : public Person {
private:
    string department;
    vector<string> subjects;
    vector<string> assignedCourses;
    Date hireDate;
    double salary;

public:
    Teacher(const string& id, const string& firstName, const string& lastName,
            const string& department);

    string getRole() const override { return "Teacher"; }
    void assignCourse(const string& courseId);
    void removeCourseAssignment(const string& courseId);
    void addSubjectSpecialization(const string& subject);
};
```

### Data Management Classes

#### Course Class
```cpp
class Course {
private:
    string courseId;
    string courseName;
    string description;
    int credits;
    string teacherId;
    vector<string> enrolledStudents;
    int maxCapacity;
    Schedule schedule;

public:
    Course(const string& id, const string& name, int credits, int maxCapacity);

    bool enrollStudent(const string& studentId);
    bool removeStudent(const string& studentId);
    bool isStudentEnrolled(const string& studentId) const;
    int getAvailableSlots() const;
    void setTeacher(const string& teacherId);
};
```

#### Grade Class
```cpp
class Grade {
private:
    string studentId;
    string courseId;
    double score;
    string gradeType; // "Quiz", "Midterm", "Final", "Assignment"
    Date dateRecorded;
    double weight; // Weight in final grade calculation

public:
    Grade(const string& studentId, const string& courseId,
          double score, const string& gradeType, double weight = 1.0);

    char getLetterGrade() const;
    double getGradePoints() const;
    bool isPassingGrade() const;
};
```

## Data Persistence and File Management

### File Structure
```
data/
├── students.dat          # Binary student data
├── teachers.dat          # Binary teacher data
├── courses.dat           # Binary course data
├── grades.dat            # Binary grade records
├── config/
│   ├── settings.ini      # Application settings
│   └── database.conf     # Database configuration
└── backups/
    ├── daily/            # Daily backup files
    └── weekly/           # Weekly backup files
```

### Data Serialization
```cpp
class DataManager {
private:
    string dataDirectory;

public:
    // Student operations
    bool saveStudents(const vector<Student>& students);
    vector<Student> loadStudents();

    // Teacher operations
    bool saveTeachers(const vector<Teacher>& teachers);
    vector<Teacher> loadTeachers();

    // Course operations
    bool saveCourses(const vector<Course>& courses);
    vector<Course> loadCourses();

    // Grade operations
    bool saveGrades(const vector<Grade>& grades);
    vector<Grade> loadGrades();

    // Backup and restore
    bool createBackup(const string& backupName);
    bool restoreFromBackup(const string& backupName);
};
```

## User Interface Design

### Main Menu System
```cpp
class MenuSystem {
private:
    SchoolManager* schoolManager;

public:
    void displayMainMenu();
    void handleStudentMenu();
    void handleTeacherMenu();
    void handleCourseMenu();
    void handleGradeMenu();
    void handleReportsMenu();
    void handleSystemMenu();

private:
    void clearScreen();
    void pauseScreen();
    int getMenuChoice(int minChoice, int maxChoice);
    void displayHeader(const string& title);
};
```

### Menu Structure
```
Main Menu
├── 1. Student Management
│   ├── Add New Student
│   ├── Search Student
│   ├── Update Student Info
│   ├── Delete Student
│   └── List All Students
├── 2. Teacher Management
│   ├── Add New Teacher
│   ├── Search Teacher
│   ├── Update Teacher Info
│   ├── Assign Courses
│   └── List All Teachers
├── 3. Course Management
│   ├── Create New Course
│   ├── Search Course
│   ├── Update Course Info
│   ├── Manage Enrollment
│   └── List All Courses
├── 4. Grade Management
│   ├── Record Grade
│   ├── Update Grade
│   ├── Calculate GPA
│   └── Grade Statistics
├── 5. Reports
│   ├── Student Reports
│   ├── Teacher Reports
│   ├── Course Reports
│   └── System Statistics
└── 6. System Settings
    ├── Backup Data
    ├── Restore Data
    ├── Import/Export
    └── Configuration
```

## Advanced Features

### Search and Filter System
```cpp
class SearchEngine {
public:
    // Student search
    vector<Student> searchStudents(const SearchCriteria& criteria);
    vector<Student> filterByGradeLevel(int gradeLevel);
    vector<Student> filterByGPA(double minGPA, double maxGPA);

    // Teacher search
    vector<Teacher> searchTeachers(const SearchCriteria& criteria);
    vector<Teacher> filterByDepartment(const string& department);
    vector<Teacher> filterBySubject(const string& subject);

    // Course search
    vector<Course> searchCourses(const SearchCriteria& criteria);
    vector<Course> filterByCredits(int credits);
    vector<Course> filterByAvailability();
};

struct SearchCriteria {
    string keyword;
    string field; // "name", "id", "email", etc.
    bool caseSensitive = false;
    bool exactMatch = false;
};
```

### Reporting System
```cpp
class ReportGenerator {
public:
    // Student reports
    StudentTranscript generateTranscript(const string& studentId);
    ClassRoster generateClassRoster(const string& courseId);
    GradeReport generateGradeReport(const string& studentId, const string& semester);

    // Teacher reports
    TeacherSchedule generateTeacherSchedule(const string& teacherId);
    CourseStatistics generateCourseStats(const string& courseId);

    // System reports
    EnrollmentSummary generateEnrollmentSummary();
    PerformanceAnalysis generatePerformanceAnalysis();

    // Export functions
    bool exportToCSV(const Report& report, const string& filename);
    bool exportToPDF(const Report& report, const string& filename);
    bool exportToHTML(const Report& report, const string& filename);
};
```

### Validation System
```cpp
class ValidationEngine {
public:
    // Student validation
    static bool validateStudentId(const string& id);
    static bool validateEmail(const string& email);
    static bool validatePhoneNumber(const string& phone);
    static bool validateGradeLevel(int gradeLevel);

    // Grade validation
    static bool validateGrade(double grade);
    static bool validateGradeType(const string& type);

    // Course validation
    static bool validateCourseId(const string& id);
    static bool validateCredits(int credits);
    static bool validateCapacity(int capacity);

    // General validation
    static bool validateDate(const Date& date);
    static bool validateName(const string& name);
    static string sanitizeInput(const string& input);
};
```

## Implementation Examples

### Complete Student Management Example
```cpp
// main.cpp - Student management implementation
void handleStudentManagement() {
    StudentManager studentManager;
    int choice;

    do {
        cout << "\n=== Student Management ===" << endl;
        cout << "1. Add New Student" << endl;
        cout << "2. Search Student" << endl;
        cout << "3. Update Student" << endl;
        cout << "4. Delete Student" << endl;
        cout << "5. List All Students" << endl;
        cout << "0. Back to Main Menu" << endl;
        cout << "Enter choice: ";
        cin >> choice;

        switch(choice) {
            case 1: addNewStudent(studentManager); break;
            case 2: searchStudent(studentManager); break;
            case 3: updateStudent(studentManager); break;
            case 4: deleteStudent(studentManager); break;
            case 5: listAllStudents(studentManager); break;
        }
    } while(choice != 0);
}

void addNewStudent(StudentManager& manager) {
    try {
        string id, firstName, lastName, email, phone;
        int gradeLevel;

        cout << "Enter Student ID: ";
        cin >> id;

        // Check if student already exists
        if (manager.studentExists(id)) {
            throw runtime_error("Student with ID " + id + " already exists!");
        }

        cout << "Enter First Name: ";
        cin >> firstName;
        cout << "Enter Last Name: ";
        cin >> lastName;
        cout << "Enter Grade Level (1-12): ";
        cin >> gradeLevel;

        if (gradeLevel < 1 || gradeLevel > 12) {
            throw invalid_argument("Grade level must be between 1 and 12");
        }

        cout << "Enter Email: ";
        cin >> email;
        cout << "Enter Phone: ";
        cin >> phone;

        Student newStudent(id, firstName, lastName, gradeLevel);
        newStudent.setEmail(email);
        newStudent.setPhoneNumber(phone);

        if (manager.addStudent(newStudent)) {
            cout << "Student added successfully!" << endl;
            manager.saveToFile(); // Auto-save
        }

    } catch (const exception& e) {
        cout << "Error: " << e.what() << endl;
    }
}
```

### Grade Management with GPA Calculation
```cpp
// grade.cpp - Advanced grade management
class GradeManager {
private:
    vector<Grade> grades;

public:
    bool recordGrade(const Grade& grade) {
        // Validate grade
        if (!ValidationEngine::validateGrade(grade.getScore())) {
            throw invalid_argument("Invalid grade score");
        }

        // Check if grade already exists for this student/course/type
        auto existing = findGrade(grade.getStudentId(),
                                 grade.getCourseId(),
                                 grade.getGradeType());

        if (existing != grades.end()) {
            // Update existing grade
            *existing = grade;
            cout << "Grade updated successfully!" << endl;
        } else {
            // Add new grade
            grades.push_back(grade);
            cout << "Grade recorded successfully!" << endl;
        }

        // Recalculate student GPA
        updateStudentGPA(grade.getStudentId());
        return true;
    }

    double calculateCourseGPA(const string& studentId, const string& courseId) {
        vector<Grade> courseGrades;
        double totalWeightedScore = 0.0;
        double totalWeight = 0.0;

        for (const auto& grade : grades) {
            if (grade.getStudentId() == studentId &&
                grade.getCourseId() == courseId) {
                courseGrades.push_back(grade);
                totalWeightedScore += grade.getScore() * grade.getWeight();
                totalWeight += grade.getWeight();
            }
        }

        return totalWeight > 0 ? totalWeightedScore / totalWeight : 0.0;
    }

    double calculateOverallGPA(const string& studentId) {
        map<string, double> courseGPAs;

        // Get all courses for student
        set<string> studentCourses;
        for (const auto& grade : grades) {
            if (grade.getStudentId() == studentId) {
                studentCourses.insert(grade.getCourseId());
            }
        }

        // Calculate GPA for each course
        double totalGPA = 0.0;
        int courseCount = 0;

        for (const string& courseId : studentCourses) {
            double courseGPA = calculateCourseGPA(studentId, courseId);
            if (courseGPA > 0) {
                totalGPA += courseGPA;
                courseCount++;
            }
        }

        return courseCount > 0 ? totalGPA / courseCount : 0.0;
    }
};
```

### Course Enrollment System
```cpp
// course.cpp - Course enrollment with capacity management
class CourseManager {
private:
    vector<Course> courses;
    StudentManager* studentManager;

public:
    bool enrollStudentInCourse(const string& studentId, const string& courseId) {
        try {
            // Find course
            auto courseIt = findCourse(courseId);
            if (courseIt == courses.end()) {
                throw runtime_error("Course not found: " + courseId);
            }

            // Check if student exists
            if (!studentManager->studentExists(studentId)) {
                throw runtime_error("Student not found: " + studentId);
            }

            // Check course capacity
            if (courseIt->getEnrolledCount() >= courseIt->getMaxCapacity()) {
                throw runtime_error("Course is at maximum capacity");
            }

            // Check if student already enrolled
            if (courseIt->isStudentEnrolled(studentId)) {
                throw runtime_error("Student already enrolled in this course");
            }

            // Check prerequisites
            if (!checkPrerequisites(studentId, courseId)) {
                throw runtime_error("Student does not meet course prerequisites");
            }

            // Enroll student
            courseIt->enrollStudent(studentId);
            studentManager->addCourseToStudent(studentId, courseId);

            cout << "Student " << studentId << " enrolled in course "
                 << courseIt->getCourseName() << " successfully!" << endl;

            return true;

        } catch (const exception& e) {
            cout << "Enrollment failed: " << e.what() << endl;
            return false;
        }
    }

    bool dropStudentFromCourse(const string& studentId, const string& courseId) {
        try {
            auto courseIt = findCourse(courseId);
            if (courseIt == courses.end()) {
                throw runtime_error("Course not found");
            }

            if (!courseIt->isStudentEnrolled(studentId)) {
                throw runtime_error("Student not enrolled in this course");
            }

            // Check if it's past drop deadline
            if (isPastDropDeadline(courseId)) {
                throw runtime_error("Drop deadline has passed for this course");
            }

            courseIt->removeStudent(studentId);
            studentManager->removeCourseFromStudent(studentId, courseId);

            cout << "Student dropped from course successfully!" << endl;
            return true;

        } catch (const exception& e) {
            cout << "Drop failed: " << e.what() << endl;
            return false;
        }
    }
};
```

### Advanced Reporting System
```cpp
// reports.cpp - Comprehensive reporting system
class AdvancedReportGenerator {
public:
    void generateStudentTranscript(const string& studentId) {
        try {
            Student student = studentManager->getStudent(studentId);
            vector<Grade> studentGrades = gradeManager->getStudentGrades(studentId);

            cout << "\n" << string(60, '=') << endl;
            cout << "           OFFICIAL TRANSCRIPT" << endl;
            cout << string(60, '=') << endl;

            cout << "Student ID: " << student.getId() << endl;
            cout << "Name: " << student.getFullName() << endl;
            cout << "Grade Level: " << student.getGradeLevel() << endl;
            cout << "Overall GPA: " << fixed << setprecision(2)
                 << gradeManager->calculateOverallGPA(studentId) << endl;

            cout << "\nCourse History:" << endl;
            cout << string(60, '-') << endl;
            cout << left << setw(12) << "Course ID"
                 << setw(25) << "Course Name"
                 << setw(8) << "Credits"
                 << setw(8) << "Grade"
                 << "GPA Points" << endl;
            cout << string(60, '-') << endl;

            map<string, vector<Grade>> courseGrades;
            for (const auto& grade : studentGrades) {
                courseGrades[grade.getCourseId()].push_back(grade);
            }

            double totalCredits = 0;
            double totalGradePoints = 0;

            for (const auto& [courseId, grades] : courseGrades) {
                Course course = courseManager->getCourse(courseId);
                double courseGPA = gradeManager->calculateCourseGPA(studentId, courseId);
                char letterGrade = convertToLetterGrade(courseGPA);

                cout << left << setw(12) << courseId
                     << setw(25) << course.getCourseName()
                     << setw(8) << course.getCredits()
                     << setw(8) << letterGrade
                     << fixed << setprecision(2) << courseGPA << endl;

                totalCredits += course.getCredits();
                totalGradePoints += courseGPA * course.getCredits();
            }

            cout << string(60, '-') << endl;
            cout << "Total Credits: " << totalCredits << endl;
            cout << "Cumulative GPA: " << fixed << setprecision(2)
                 << (totalCredits > 0 ? totalGradePoints / totalCredits : 0.0) << endl;
            cout << string(60, '=') << endl;

        } catch (const exception& e) {
            cout << "Error generating transcript: " << e.what() << endl;
        }
    }

    void generateClassRoster(const string& courseId) {
        try {
            Course course = courseManager->getCourse(courseId);
            vector<string> enrolledStudents = course.getEnrolledStudents();

            cout << "\n" << string(50, '=') << endl;
            cout << "CLASS ROSTER - " << course.getCourseName() << endl;
            cout << "Course ID: " << courseId << endl;
            cout << "Instructor: " << getTeacherName(course.getTeacherId()) << endl;
            cout << "Enrolled: " << enrolledStudents.size()
                 << "/" << course.getMaxCapacity() << endl;
            cout << string(50, '=') << endl;

            cout << left << setw(12) << "Student ID"
                 << setw(25) << "Name"
                 << setw(8) << "Grade"
                 << "Email" << endl;
            cout << string(50, '-') << endl;

            for (const string& studentId : enrolledStudents) {
                Student student = studentManager->getStudent(studentId);
                double courseGPA = gradeManager->calculateCourseGPA(studentId, courseId);
                char letterGrade = courseGPA > 0 ? convertToLetterGrade(courseGPA) : 'N';

                cout << left << setw(12) << studentId
                     << setw(25) << student.getFullName()
                     << setw(8) << letterGrade
                     << student.getEmail() << endl;
            }

            cout << string(50, '=') << endl;

        } catch (const exception& e) {
            cout << "Error generating roster: " << e.what() << endl;
        }
    }
};
```

## Security and Data Protection

### User Authentication System
```cpp
class AuthenticationManager {
private:
    map<string, UserAccount> userAccounts;
    string currentUser;
    UserRole currentUserRole;

public:
    enum class UserRole {
        ADMIN,
        TEACHER,
        STUDENT,
        GUEST
    };

    bool login(const string& username, const string& password) {
        auto it = userAccounts.find(username);
        if (it != userAccounts.end() && it->second.verifyPassword(password)) {
            currentUser = username;
            currentUserRole = it->second.getRole();
            logLoginAttempt(username, true);
            return true;
        }

        logLoginAttempt(username, false);
        return false;
    }

    bool hasPermission(const string& operation) const {
        switch (currentUserRole) {
            case UserRole::ADMIN:
                return true; // Admin has all permissions
            case UserRole::TEACHER:
                return isTeacherOperation(operation);
            case UserRole::STUDENT:
                return isStudentOperation(operation);
            default:
                return false;
        }
    }

private:
    void logLoginAttempt(const string& username, bool success) {
        ofstream logFile("logs/login_attempts.log", ios::app);
        logFile << getCurrentTimestamp() << " - "
                << username << " - "
                << (success ? "SUCCESS" : "FAILED") << endl;
    }
};
```

### Data Encryption and Backup
```cpp
class SecurityManager {
public:
    // Simple encryption for sensitive data
    string encryptData(const string& data, const string& key) {
        string encrypted = data;
        for (size_t i = 0; i < encrypted.length(); ++i) {
            encrypted[i] ^= key[i % key.length()];
        }
        return base64Encode(encrypted);
    }

    string decryptData(const string& encryptedData, const string& key) {
        string decoded = base64Decode(encryptedData);
        for (size_t i = 0; i < decoded.length(); ++i) {
            decoded[i] ^= key[i % key.length()];
        }
        return decoded;
    }

    // Secure backup creation
    bool createSecureBackup(const string& backupPath) {
        try {
            // Create backup directory
            filesystem::create_directories(backupPath);

            // Backup all data files with encryption
            vector<string> dataFiles = {
                "data/students.dat",
                "data/teachers.dat",
                "data/courses.dat",
                "data/grades.dat"
            };

            string encryptionKey = generateBackupKey();

            for (const string& file : dataFiles) {
                if (filesystem::exists(file)) {
                    string content = readFileContent(file);
                    string encrypted = encryptData(content, encryptionKey);

                    string backupFile = backupPath + "/" +
                                       filesystem::path(file).filename().string() + ".bak";
                    writeFileContent(backupFile, encrypted);
                }
            }

            // Save encryption key securely
            saveBackupKey(backupPath + "/backup.key", encryptionKey);

            cout << "Secure backup created successfully at: " << backupPath << endl;
            return true;

        } catch (const exception& e) {
            cout << "Backup failed: " << e.what() << endl;
            return false;
        }
    }
};
```

## Testing Framework

### Unit Testing Setup
```cpp
// tests/test_student.cpp
#include <gtest/gtest.h>
#include "../include/student.h"

class StudentTest : public ::testing::Test {
protected:
    void SetUp() override {
        testStudent = new Student("S001", "John", "Doe", 10, "10A");
    }

    void TearDown() override {
        delete testStudent;
    }

    Student* testStudent;
};

TEST_F(StudentTest, StudentCreation) {
    EXPECT_EQ(testStudent->getId(), "S001");
    EXPECT_EQ(testStudent->getFirstName(), "John");
    EXPECT_EQ(testStudent->getLastName(), "Doe");
    EXPECT_EQ(testStudent->getGradeLevel(), 10);
}

TEST_F(StudentTest, CourseEnrollment) {
    testStudent->enrollInCourse("MATH101");
    EXPECT_TRUE(testStudent->isEnrolledInCourse("MATH101"));

    testStudent->dropCourse("MATH101");
    EXPECT_FALSE(testStudent->isEnrolledInCourse("MATH101"));
}

TEST_F(StudentTest, GPACalculation) {
    // Add some grades
    testStudent->addGrade(Grade("S001", "MATH101", 85.0, "Final"));
    testStudent->addGrade(Grade("S001", "ENG101", 92.0, "Final"));

    double gpa = testStudent->calculateGPA();
    EXPECT_NEAR(gpa, 88.5, 0.1); // Expected average
}

// Grade validation tests
TEST(GradeTest, ValidGradeRange) {
    EXPECT_NO_THROW(Grade("S001", "MATH101", 85.0, "Quiz"));
    EXPECT_THROW(Grade("S001", "MATH101", -5.0, "Quiz"), invalid_argument);
    EXPECT_THROW(Grade("S001", "MATH101", 105.0, "Quiz"), invalid_argument);
}

// Course capacity tests
TEST(CourseTest, EnrollmentCapacity) {
    Course course("CS101", "Introduction to Programming", 3, 2); // Max 2 students

    EXPECT_TRUE(course.enrollStudent("S001"));
    EXPECT_TRUE(course.enrollStudent("S002"));
    EXPECT_FALSE(course.enrollStudent("S003")); // Should fail - capacity reached
}
```

### Integration Testing
```cpp
// tests/test_integration.cpp
class IntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        studentManager = new StudentManager();
        courseManager = new CourseManager();
        gradeManager = new GradeManager();
    }

    void TearDown() override {
        delete studentManager;
        delete courseManager;
        delete gradeManager;
    }

    StudentManager* studentManager;
    CourseManager* courseManager;
    GradeManager* gradeManager;
};

TEST_F(IntegrationTest, CompleteStudentWorkflow) {
    // Create student
    Student student("S001", "Alice", "Smith", 11, "11A");
    EXPECT_TRUE(studentManager->addStudent(student));

    // Create course
    Course course("MATH101", "Algebra", 3, 30);
    EXPECT_TRUE(courseManager->addCourse(course));

    // Enroll student in course
    EXPECT_TRUE(courseManager->enrollStudentInCourse("S001", "MATH101"));

    // Record grades
    Grade grade1("S001", "MATH101", 85.0, "Quiz", 0.2);
    Grade grade2("S001", "MATH101", 92.0, "Midterm", 0.3);
    Grade grade3("S001", "MATH101", 88.0, "Final", 0.5);

    EXPECT_TRUE(gradeManager->recordGrade(grade1));
    EXPECT_TRUE(gradeManager->recordGrade(grade2));
    EXPECT_TRUE(gradeManager->recordGrade(grade3));

    // Calculate final course grade
    double courseGrade = gradeManager->calculateCourseGPA("S001", "MATH101");
    double expected = (85.0 * 0.2) + (92.0 * 0.3) + (88.0 * 0.5);
    EXPECT_NEAR(courseGrade, expected, 0.1);
}
```

### Performance Testing
```cpp
// tests/test_performance.cpp
TEST(PerformanceTest, LargeDatasetHandling) {
    StudentManager manager;

    // Test with 10,000 students
    auto start = chrono::high_resolution_clock::now();

    for (int i = 0; i < 10000; ++i) {
        string id = "S" + to_string(i + 1);
        Student student(id, "Student", to_string(i), 9 + (i % 4), "Class" + to_string(i % 10));
        manager.addStudent(student);
    }

    auto end = chrono::high_resolution_clock::now();
    auto duration = chrono::duration_cast<chrono::milliseconds>(end - start);

    cout << "Added 10,000 students in " << duration.count() << " ms" << endl;
    EXPECT_LT(duration.count(), 5000); // Should complete within 5 seconds

    // Test search performance
    start = chrono::high_resolution_clock::now();
    auto result = manager.searchStudents(SearchCriteria{"Student5000", "name", false, true});
    end = chrono::high_resolution_clock::now();

    duration = chrono::duration_cast<chrono::milliseconds>(end - start);
    cout << "Search completed in " << duration.count() << " ms" << endl;
    EXPECT_LT(duration.count(), 100); // Search should be fast
}
```

## Configuration Management

### Settings Configuration
```cpp
// config/settings.ini
[Database]
host=localhost
port=3306
username=school_admin
password=encrypted_password
database=school_management

[Application]
max_students_per_course=50
backup_interval_hours=24
log_level=INFO
data_directory=./data
backup_directory=./backups

[Security]
session_timeout_minutes=30
max_login_attempts=3
password_min_length=8
require_password_complexity=true

[Reports]
default_format=PDF
include_photos=false
watermark_text=CONFIDENTIAL
```

### Configuration Loader
```cpp
class ConfigurationManager {
private:
    map<string, map<string, string>> config;

public:
    bool loadConfiguration(const string& configFile) {
        ifstream file(configFile);
        if (!file.is_open()) {
            cout << "Warning: Configuration file not found. Using defaults." << endl;
            loadDefaultConfiguration();
            return false;
        }

        string line, currentSection;
        while (getline(file, line)) {
            line = trim(line);

            if (line.empty() || line[0] == '#') continue;

            if (line[0] == '[' && line.back() == ']') {
                currentSection = line.substr(1, line.length() - 2);
            } else {
                size_t pos = line.find('=');
                if (pos != string::npos) {
                    string key = trim(line.substr(0, pos));
                    string value = trim(line.substr(pos + 1));
                    config[currentSection][key] = value;
                }
            }
        }

        return true;
    }

    string getString(const string& section, const string& key, const string& defaultValue = "") {
        if (config.count(section) && config[section].count(key)) {
            return config[section][key];
        }
        return defaultValue;
    }

    int getInt(const string& section, const string& key, int defaultValue = 0) {
        string value = getString(section, key);
        return value.empty() ? defaultValue : stoi(value);
    }

    bool getBool(const string& section, const string& key, bool defaultValue = false) {
        string value = getString(section, key);
        if (value.empty()) return defaultValue;

        transform(value.begin(), value.end(), value.begin(), ::tolower);
        return value == "true" || value == "1" || value == "yes";
    }
};
```

## Deployment and Distribution

### CMake Installation Configuration
```cmake
# Add to CMakeLists.txt for installation
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(DIRECTORY config/
    DESTINATION share/${PROJECT_NAME}/config
    FILES_MATCHING PATTERN "*.ini"
)

install(DIRECTORY docs/
    DESTINATION share/doc/${PROJECT_NAME}
    FILES_MATCHING PATTERN "*.txt" PATTERN "*.md"
)

# Create package
set(CPACK_PACKAGE_NAME "SchoolManagementSystem")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "Comprehensive School Management System")
set(CPACK_PACKAGE_VENDOR "Educational Software Solutions")

include(CPack)
```

### Build Scripts
```bash
#!/bin/bash
# build.sh - Automated build script

echo "Building School Management System..."

# Create build directory
mkdir -p build
cd build

# Configure with CMake
cmake --preset=release ..

# Build the project
cmake --build . --preset=release

# Run tests
ctest --output-on-failure

# Create package
cpack

echo "Build completed successfully!"
```

This comprehensive documentation provides a complete guide for implementing, testing, and deploying the School Management System while maintaining professional standards and robust error handling throughout the application.
