===============================================================================
                    دليل نظام إدارة المدرسة - School Management System
===============================================================================

المحتويات:
1. نظرة عامة على المشروع
2. هيكل المشروع والملفات
3. شرح ملف CMakeLists.txt بالتفصيل
4. شرح ملف CMakePresets.json بالتفصيل
5. عملية البناء والتجميع (Compilation Process)
6. مكونات البرنامج الأساسية
7. كيفية تشغيل البرنامج
8. استكشاف الأخطاء وحلها

===============================================================================
1. نظرة عامة على المشروع
===============================================================================

نظام إدارة المدرسة هو تطبيق C++ شامل مصمم لإدارة العمليات الأساسية في المدرسة:
- إدارة الطلاب (إضافة، بحث، تحديث، حذف)
- إدارة المدرسين (إضافة، بحث، عرض القوائم)
- إدارة المقررات (إضافة، تسجيل الطلاب، إدارة السعة)
- إدارة الدرجات (تسجيل، عرض، حساب المعدلات)
- تقارير شاملة وإحصائيات النظام

المشروع يستخدم:
- C++17 كمعيار البرمجة
- CMake كنظام البناء
- MinGW كمترجم على Windows
- البرمجة الكائنية (OOP) مع الوراثة والتغليف

===============================================================================
2. هيكل المشروع والملفات
===============================================================================

المجلد الجذر: c:\6_ school\
├── CMakeLists.txt              # ملف إعداد CMake الرئيسي
├── CMakePresets.json           # إعدادات CMake المسبقة
├── project_documentation.txt   # الوثائق الأصلية للمشروع
├── project_guide.txt          # هذا الدليل
├── src/                       # مجلد الملفات المصدرية
│   ├── main.cpp               # الملف الرئيسي للتطبيق
│   ├── student.cpp            # تنفيذ كلاس الطالب
│   ├── teacher.cpp            # تنفيذ كلاس المدرس
│   ├── course.cpp             # تنفيذ كلاس المقرر
│   └── grade.cpp              # تنفيذ كلاس الدرجات
├── include/                   # مجلد ملفات الرؤوس
│   ├── student.h              # تعريف كلاس الطالب
│   ├── teacher.h              # تعريف كلاس المدرس
│   ├── course.h               # تعريف كلاس المقرر
│   └── grade.h                # تعريف كلاس الدرجات
├── tests/                     # مجلد الاختبارات (فارغ حالياً)
├── config/                    # مجلد ملفات الإعداد (فارغ حالياً)
├── data/                      # مجلد البيانات (فارغ حالياً)
└── build/                     # مجلد البناء والملفات المترجمة
    ├── bin/                   # مجلد الملفات التنفيذية
    │   └── SchoolManagementSystem.exe
    ├── CMakeFiles/            # ملفات CMake المؤقتة
    ├── CMakeCache.txt         # ذاكرة التخزين المؤقت لـ CMake
    ├── cmake_install.cmake    # سكريبت التثبيت
    └── Makefile              # ملف Make المُولد

===============================================================================
3. شرح ملف CMakeLists.txt بالتفصيل
===============================================================================

CMakeLists.txt هو ملف الإعداد الرئيسي لنظام البناء CMake:

```cmake
# تحديد الحد الأدنى لإصدار CMake المطلوب
cmake_minimum_required(VERSION 3.20)

# تعريف اسم المشروع
project(SchoolManagementSystem)

# تحديد معيار C++ المستخدم (C++17)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# تحديد مجلد الملفات المصدرية
set(SOURCES
    src/main.cpp
    src/student.cpp
    src/teacher.cpp
    src/course.cpp
    src/grade.cpp
)

# تحديد مجلد ملفات الرؤوس
include_directories(include)

# إنشاء الملف التنفيذي
add_executable(${PROJECT_NAME} ${SOURCES})

# تحديد مجلد الإخراج للملفات التنفيذية
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# إعدادات المترجم (اختيارية)
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra)
endif()
```

شرح كل سطر:
- cmake_minimum_required: يحدد أقل إصدار CMake مطلوب (3.20)
- project: يعرف اسم المشروع كـ "SchoolManagementSystem"
- CMAKE_CXX_STANDARD: يحدد استخدام C++17
- SOURCES: متغير يحتوي على قائمة جميع الملفات المصدرية
- include_directories: يضيف مجلد include لمسار البحث عن الرؤوس
- add_executable: ينشئ هدف تنفيذي باسم المشروع
- RUNTIME_OUTPUT_DIRECTORY: يحدد مجلد bin كمكان للملف التنفيذي
- target_compile_options: يضيف خيارات تحذيرات للمترجم GNU

===============================================================================
4. شرح ملف CMakePresets.json بالتفصيل
===============================================================================

CMakePresets.json يحتوي على إعدادات مسبقة لتسهيل عملية البناء:

```json
{
    "version": 3,
    "configurePresets": [
        {
            "name": "default",
            "displayName": "Default Config",
            "description": "Default build configuration",
            "generator": "MinGW Makefiles",
            "binaryDir": "${sourceDir}/build",
            "cacheVariables": {
                "CMAKE_BUILD_TYPE": "Debug"
            }
        },
        {
            "name": "release",
            "displayName": "Release Config", 
            "description": "Release build configuration",
            "generator": "MinGW Makefiles",
            "binaryDir": "${sourceDir}/build-release",
            "cacheVariables": {
                "CMAKE_BUILD_TYPE": "Release"
            }
        }
    ],
    "buildPresets": [
        {
            "name": "default",
            "configurePreset": "default"
        },
        {
            "name": "release",
            "configurePreset": "release"
        }
    ]
}
```

شرح المكونات:
- version: إصدار مخطط CMakePresets (الإصدار 3)
- configurePresets: إعدادات التكوين المختلفة
  * name: اسم الإعداد المسبق
  * displayName: الاسم المعروض للمستخدم
  * generator: نوع المولد المستخدم (MinGW Makefiles)
  * binaryDir: مجلد البناء
  * cacheVariables: متغيرات CMake المخزنة مؤقتاً
    - CMAKE_BUILD_TYPE: نوع البناء (Debug أو Release)

- buildPresets: إعدادات البناء المرتبطة بإعدادات التكوين

الفرق بين Debug و Release:
- Debug: يحتوي على معلومات التصحيح، أبطأ في التنفيذ
- Release: محسن للأداء، أسرع في التنفيذ، بدون معلومات تصحيح

===============================================================================
5. عملية البناء والتجميع (Compilation Process)
===============================================================================

عملية البناء تتم على مراحل:

المرحلة 1: إعداد البيئة
------------------------
1. التأكد من وجود CMake (الإصدار 3.20 أو أحدث)
2. التأكد من وجود مترجم C++ (MinGW في حالتنا)
3. إنشاء مجلد البناء إذا لم يكن موجوداً

الأوامر المستخدمة:
```powershell
# إنشاء مجلد البناء
New-Item -ItemType Directory -Path "build" -Force

# الانتقال إلى مجلد البناء
cd build
```

المرحلة 2: تكوين المشروع (Configure)
------------------------------------
CMake يقرأ CMakeLists.txt وينشئ ملفات البناء المناسبة للنظام:

```powershell
# التكوين باستخدام MinGW
cmake .. -G "MinGW Makefiles"

# أو باستخدام الإعداد المسبق
cmake --preset=default
```

ما يحدث في هذه المرحلة:
- CMake يفحص النظام ويجد المترجم
- ينشئ CMakeCache.txt لحفظ الإعدادات
- ينشئ Makefile أو ملفات البناء المناسبة
- يتحقق من وجود جميع التبعيات المطلوبة

المرحلة 3: البناء (Build)
--------------------------
```powershell
# البناء باستخدام CMake
cmake --build .

# أو باستخدام Make مباشرة
mingw32-make
```

ما يحدث في هذه المرحلة:
1. المترجم يقرأ كل ملف .cpp
2. ينشئ ملفات كائن (.obj) لكل ملف مصدري
3. الرابط (Linker) يجمع جميع ملفات الكائن
4. ينشئ الملف التنفيذي النهائي (.exe)

تسلسل التجميع:
```
src/main.cpp     → CMakeFiles/SchoolManagementSystem.dir/src/main.cpp.obj
src/student.cpp  → CMakeFiles/SchoolManagementSystem.dir/src/student.cpp.obj  
src/teacher.cpp  → CMakeFiles/SchoolManagementSystem.dir/src/teacher.cpp.obj
src/course.cpp   → CMakeFiles/SchoolManagementSystem.dir/src/course.cpp.obj
src/grade.cpp    → CMakeFiles/SchoolManagementSystem.dir/src/grade.cpp.obj
                 ↓
            bin/SchoolManagementSystem.exe
```

المرحلة 4: التشغيل
------------------
```powershell
# تشغيل البرنامج
.\bin\SchoolManagementSystem.exe
```

===============================================================================
6. مكونات البرنامج الأساسية
===============================================================================

6.1 الكلاسات الأساسية (Core Classes)
------------------------------------

أ) كلاس Student (الطالب):
-------------------------
الملفات: include/student.h, src/student.cpp

الخصائص الأساسية:
- string id: رقم الطالب الفريد
- string firstName: الاسم الأول
- string lastName: الاسم الأخير
- int gradeLevel: المستوى الدراسي (1-12)
- string studentClass: الفصل الدراسي
- string email: البريد الإلكتروني
- string phoneNumber: رقم الهاتف
- double gpa: المعدل التراكمي
- vector<string> enrolledCourses: المقررات المسجلة

الوظائف الرئيسية:
- displayInfo(): عرض معلومات الطالب
- enrollInCourse(): تسجيل في مقرر
- dropCourse(): إلغاء تسجيل مقرر
- updateGPA(): تحديث المعدل التراكمي
- getFullName(): الحصول على الاسم الكامل

ب) كلاس Teacher (المدرس):
--------------------------
الملفات: include/teacher.h, src/teacher.cpp

الخصائص الأساسية:
- string id: رقم المدرس الفريد
- string firstName, lastName: الاسم
- string department: القسم
- string email, phoneNumber: معلومات الاتصال
- double salary: الراتب
- vector<string> subjects: المواد المتخصصة
- vector<string> assignedCourses: المقررات المُدرسة

الوظائف الرئيسية:
- displayInfo(): عرض معلومات المدرس
- assignCourse(): تعيين مقرر للتدريس
- addSubject(): إضافة مادة تخصص
- setSalary(): تحديد الراتب

ج) كلاس Course (المقرر):
-------------------------
الملفات: include/course.h, src/course.cpp

الخصائص الأساسية:
- string courseId: رقم المقرر الفريد
- string courseName: اسم المقرر
- int credits: عدد الساعات المعتمدة
- int maxCapacity: السعة القصوى
- string teacherId: رقم المدرس المُدرس
- string description: وصف المقرر
- vector<string> enrolledStudents: الطلاب المسجلين
- string schedule: الجدول الزمني

الوظائف الرئيسية:
- enrollStudent(): تسجيل طالب
- dropStudent(): إلغاء تسجيل طالب
- isFull(): فحص امتلاء المقرر
- displayInfo(): عرض معلومات المقرر
- displayEnrolledStudents(): عرض الطلاب المسجلين

د) كلاس Grade (الدرجة):
-----------------------
الملفات: include/grade.h, src/grade.cpp

الخصائص الأساسية:
- string studentId: رقم الطالب
- string courseId: رقم المقرر
- double score: الدرجة (0-100)
- string gradeType: نوع التقييم (Quiz/Midterm/Final/Assignment)
- double weight: الوزن النسبي
- string letterGrade: الدرجة الحرفية (A, B, C, D, F)

هـ) كلاس GradeManager (مدير الدرجات):
------------------------------------
وظائف إدارة شاملة للدرجات:
- addGrade(): إضافة درجة جديدة
- calculateCourseGPA(): حساب معدل المقرر
- calculateOverallGPA(): حساب المعدل العام
- displayStudentGrades(): عرض درجات طالب
- displayCourseGrades(): عرض درجات مقرر
- getGradeDistribution(): توزيع الدرجات

6.2 النظام الرئيسي (Main System)
--------------------------------

كلاس SchoolManagementSystem:
هو الكلاس الرئيسي الذي يدير جميع العمليات:

الخصائص:
- vector<Student> students: قائمة الطلاب
- vector<Teacher> teachers: قائمة المدرسين
- vector<Course> courses: قائمة المقررات
- GradeManager gradeManager: مدير الدرجات

الوظائف الرئيسية:
- run(): تشغيل النظام الرئيسي
- displayMainMenu(): عرض القائمة الرئيسية
- handleStudentMenu(): إدارة قائمة الطلاب
- handleTeacherMenu(): إدارة قائمة المدرسين
- handleCourseMenu(): إدارة قائمة المقررات
- handleGradeMenu(): إدارة قائمة الدرجات
- handleReportsMenu(): إدارة التقارير

===============================================================================
7. كيفية تشغيل البرنامج
===============================================================================

7.1 متطلبات النظام:
-------------------
- نظام التشغيل: Windows 10/11
- المترجم: MinGW-w64 أو Visual Studio
- CMake: الإصدار 3.20 أو أحدث
- ذاكرة: 512 MB RAM كحد أدنى
- مساحة القرص: 100 MB

7.2 خطوات التشغيل:
------------------

الخطوة 1: فتح PowerShell في مجلد المشروع
```powershell
cd "c:\6_ school"
```

الخطوة 2: إنشاء مجلد البناء (إذا لم يكن موجوداً)
```powershell
New-Item -ItemType Directory -Path "build" -Force
cd build
```

الخطوة 3: تكوين المشروع
```powershell
cmake .. -G "MinGW Makefiles"
```

الخطوة 4: بناء المشروع
```powershell
cmake --build .
```

الخطوة 5: تشغيل البرنامج
```powershell
.\bin\SchoolManagementSystem.exe
```

7.3 استخدام البرنامج:
---------------------

عند تشغيل البرنامج، ستظهر القائمة الرئيسية:
```
==================================================
  MAIN MENU
==================================================
1. Student Management
2. Teacher Management
3. Course Management
4. Grade Management
5. Reports
6. System Statistics
0. Exit
```

مثال على إضافة طالب جديد:
1. اختر "1" للدخول إلى إدارة الطلاب
2. اختر "1" لإضافة طالب جديد
3. أدخل البيانات المطلوبة:
   - رقم الطالب: S001
   - الاسم الأول: Ahmed
   - الاسم الأخير: Ali
   - المستوى الدراسي: 10
   - الفصل: 10A
   - البريد الإلكتروني: <EMAIL>
   - رقم الهاتف: 0123456789

===============================================================================
8. استكشاف الأخطاء وحلها
===============================================================================

8.1 أخطاء التجميع الشائعة:
---------------------------

خطأ: "CMake Error: Generator Visual Studio 17 2022 could not find any instance"
الحل: تغيير المولد إلى MinGW:
```powershell
cmake .. -G "MinGW Makefiles"
```

خطأ: "'setprecision' is not a member of 'std'"
الحل: إضافة #include <iomanip> في بداية الملف

خطأ: "mingw32-make: command not found"
الحل: التأكد من تثبيت MinGW وإضافته إلى PATH

8.2 أخطاء وقت التشغيل:
-----------------------

خطأ: البرنامج لا يستجيب للإدخال
الحل: التأكد من استخدام PowerShell وليس Command Prompt

خطأ: "Access Denied" عند إنشاء الملفات
الحل: تشغيل PowerShell كمدير (Run as Administrator)

8.3 نصائح للتطوير:
------------------

1. استخدم Debug build أثناء التطوير:
```powershell
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug
```

2. لإعادة البناء الكامل:
```powershell
Remove-Item -Recurse -Force build
New-Item -ItemType Directory -Path "build" -Force
cd build
cmake .. -G "MinGW Makefiles"
cmake --build .
```

3. لفحص الأخطاء بالتفصيل:
```powershell
cmake --build . --verbose
```

===============================================================================
ملاحظات إضافية:
===============================================================================

- جميع الملفات تستخدم ترميز UTF-8
- النظام يدعم اللغة الإنجليزية في الواجهة
- البيانات تُحفظ في الذاكرة فقط (لا يوجد حفظ دائم حالياً)
- يمكن توسيع النظام بإضافة قاعدة بيانات مستقبلاً
- الكود يتبع معايير C++17 الحديثة

للمزيد من المساعدة أو الاستفسارات، يرجى مراجعة التوثيق الفني أو الاتصال بفريق التطوير.
