#include "course.h"
#include <algorithm>
#include <iomanip>

// Default constructor
Course::Course() : courseId(""), courseName(""), description(""), credits(0),
                   teacherId(""), maxCapacity(0), schedule("") {}

// Parameterized constructor
Course::Course(const std::string& courseId, const std::string& courseName,
               int credits, int maxCapacity)
    : courseId(courseId), courseName(courseName), description(""), credits(credits),
      teacherId(""), maxCapacity(maxCapacity), schedule("") {}

// Student enrollment management
bool Course::enrollStudent(const std::string& studentId) {
    if (isStudentEnrolled(studentId)) {
        return false; // Student already enrolled
    }
    if (isFull()) {
        return false; // Course is at capacity
    }
    enrolledStudents.push_back(studentId);
    return true;
}

bool Course::removeStudent(const std::string& studentId) {
    auto it = std::find(enrolledStudents.begin(), enrolledStudents.end(), studentId);
    if (it != enrolledStudents.end()) {
        enrolledStudents.erase(it);
        return true;
    }
    return false; // Student not found
}

bool Course::isStudentEnrolled(const std::string& studentId) const {
    return std::find(enrolledStudents.begin(), enrolledStudents.end(), studentId) 
           != enrolledStudents.end();
}

// Utility functions
void Course::displayInfo() const {
    std::cout << "\n=== Course Information ===" << std::endl;
    std::cout << "Course ID: " << courseId << std::endl;
    std::cout << "Course Name: " << courseName << std::endl;
    std::cout << "Description: " << (description.empty() ? "No description" : description) << std::endl;
    std::cout << "Credits: " << credits << std::endl;
    std::cout << "Teacher ID: " << (teacherId.empty() ? "Not assigned" : teacherId) << std::endl;
    std::cout << "Schedule: " << (schedule.empty() ? "Not scheduled" : schedule) << std::endl;
    std::cout << "Capacity: " << getEnrolledCount() << "/" << maxCapacity 
              << " (Available: " << getAvailableSlots() << ")" << std::endl;
    std::cout << "==========================" << std::endl;
}

void Course::displayEnrolledStudents() const {
    std::cout << "\n=== Enrolled Students ===" << std::endl;
    std::cout << "Course: " << courseName << " (" << courseId << ")" << std::endl;
    std::cout << "Total Enrolled: " << getEnrolledCount() << std::endl;
    
    if (enrolledStudents.empty()) {
        std::cout << "No students enrolled." << std::endl;
    } else {
        std::cout << "Student IDs:" << std::endl;
        for (size_t i = 0; i < enrolledStudents.size(); ++i) {
            std::cout << (i + 1) << ". " << enrolledStudents[i] << std::endl;
        }
    }
    std::cout << "=========================" << std::endl;
}

bool Course::isValid() const {
    return !courseId.empty() && !courseName.empty() && credits > 0 && maxCapacity > 0;
}

// Operators
bool Course::operator==(const Course& other) const {
    return courseId == other.courseId;
}

std::ostream& operator<<(std::ostream& os, const Course& course) {
    os << "Course[ID: " << course.courseId << ", Name: " << course.courseName 
       << ", Credits: " << course.credits << ", Enrolled: " 
       << course.getEnrolledCount() << "/" << course.maxCapacity << "]";
    return os;
}
