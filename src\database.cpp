#include "database.h"
#include <iostream>
#include <sstream>
#include <random>
#include <iomanip>

Database::Database(const std::string& dbPath) : db(nullptr), dbPath(dbPath) {
}

Database::~Database() {
    close();
}

bool Database::initialize() {
    int result = sqlite3_open(dbPath.c_str(), &db);
    if (result != SQLITE_OK) {
        std::cerr << "Cannot open database: " << sqlite3_errmsg(db) << std::endl;
        return false;
    }
    
    // Enable foreign keys
    executeSQL("PRAGMA foreign_keys = ON;");
    
    return createTables();
}

bool Database::createTables() {
    // Students table
    std::string createStudentsTable = R"(
        CREATE TABLE IF NOT EXISTS students (
            id TEXT PRIMARY KEY,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            grade_level INTEGER NOT NULL CHECK(grade_level >= 1 AND grade_level <= 12),
            student_class TEXT,
            email TEXT,
            phone_number TEXT,
            gpa REAL DEFAULT 0.0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
    )";
    
    // Teachers table
    std::string createTeachersTable = R"(
        CREATE TABLE IF NOT EXISTS teachers (
            id TEXT PRIMARY KEY,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            department TEXT NOT NULL,
            email TEXT,
            phone_number TEXT,
            salary REAL DEFAULT 0.0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
    )";
    
    // Courses table
    std::string createCoursesTable = R"(
        CREATE TABLE IF NOT EXISTS courses (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            credits INTEGER NOT NULL DEFAULT 3,
            max_capacity INTEGER NOT NULL DEFAULT 30,
            teacher_id TEXT,
            description TEXT,
            schedule TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (teacher_id) REFERENCES teachers(id)
        );
    )";
    
    // Grades table
    std::string createGradesTable = R"(
        CREATE TABLE IF NOT EXISTS grades (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id TEXT NOT NULL,
            course_id TEXT NOT NULL,
            score REAL NOT NULL CHECK(score >= 0 AND score <= 100),
            grade_type TEXT NOT NULL,
            weight REAL DEFAULT 1.0,
            letter_grade TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id),
            FOREIGN KEY (course_id) REFERENCES courses(id),
            UNIQUE(student_id, course_id, grade_type)
        );
    )";
    
    // Enrollments table (many-to-many relationship between students and courses)
    std::string createEnrollmentsTable = R"(
        CREATE TABLE IF NOT EXISTS enrollments (
            student_id TEXT NOT NULL,
            course_id TEXT NOT NULL,
            enrollment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (student_id, course_id),
            FOREIGN KEY (student_id) REFERENCES students(id),
            FOREIGN KEY (course_id) REFERENCES courses(id)
        );
    )";
    
    // Teacher subjects table
    std::string createTeacherSubjectsTable = R"(
        CREATE TABLE IF NOT EXISTS teacher_subjects (
            teacher_id TEXT NOT NULL,
            subject TEXT NOT NULL,
            PRIMARY KEY (teacher_id, subject),
            FOREIGN KEY (teacher_id) REFERENCES teachers(id)
        );
    )";
    
    return executeSQL(createStudentsTable) &&
           executeSQL(createTeachersTable) &&
           executeSQL(createCoursesTable) &&
           executeSQL(createGradesTable) &&
           executeSQL(createEnrollmentsTable) &&
           executeSQL(createTeacherSubjectsTable);
}

bool Database::executeSQL(const std::string& sql) {
    char* errorMessage = nullptr;
    int result = sqlite3_exec(db, sql.c_str(), nullptr, nullptr, &errorMessage);
    
    if (result != SQLITE_OK) {
        std::cerr << "SQL error: " << errorMessage << std::endl;
        sqlite3_free(errorMessage);
        return false;
    }
    
    return true;
}

sqlite3_stmt* Database::prepareStatement(const std::string& sql) {
    sqlite3_stmt* stmt;
    int result = sqlite3_prepare_v2(db, sql.c_str(), -1, &stmt, nullptr);
    
    if (result != SQLITE_OK) {
        std::cerr << "Failed to prepare statement: " << sqlite3_errmsg(db) << std::endl;
        return nullptr;
    }
    
    return stmt;
}

void Database::finalizeStatement(sqlite3_stmt* stmt) {
    if (stmt) {
        sqlite3_finalize(stmt);
    }
}

void Database::close() {
    if (db) {
        sqlite3_close(db);
        db = nullptr;
    }
}

// Student operations
bool Database::insertStudent(const Student& student) {
    std::string sql = R"(
        INSERT INTO students (id, first_name, last_name, grade_level, student_class, email, phone_number, gpa)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?);
    )";
    
    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return false;
    
    sqlite3_bind_text(stmt, 1, student.getId().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, student.getFirstName().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 3, student.getLastName().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 4, student.getGradeLevel());
    sqlite3_bind_text(stmt, 5, student.getStudentClass().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 6, student.getEmail().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 7, student.getPhoneNumber().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_double(stmt, 8, student.getGPA());
    
    int result = sqlite3_step(stmt);
    finalizeStatement(stmt);
    
    if (result != SQLITE_DONE) {
        std::cerr << "Failed to insert student: " << sqlite3_errmsg(db) << std::endl;
        return false;
    }
    
    // Insert enrolled courses
    for (const auto& courseId : student.getEnrolledCourses()) {
        enrollStudentInCourse(student.getId(), courseId);
    }
    
    return true;
}

Student Database::getStudent(const std::string& studentId) {
    std::string sql = R"(
        SELECT id, first_name, last_name, grade_level, student_class, email, phone_number, gpa
        FROM students WHERE id = ?;
    )";
    
    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return Student();
    
    sqlite3_bind_text(stmt, 1, studentId.c_str(), -1, SQLITE_STATIC);
    
    Student student;
    if (sqlite3_step(stmt) == SQLITE_ROW) {
        student = Student(
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0)), // id
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1)), // first_name
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2)), // last_name
            sqlite3_column_int(stmt, 3), // grade_level
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 4)), // student_class
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 5)), // email
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 6))  // phone_number
        );
        student.setGPA(sqlite3_column_double(stmt, 7));
        
        // Load enrolled courses
        auto courses = getStudentCourses(studentId);
        for (const auto& courseId : courses) {
            student.enrollInCourse(courseId);
        }
    }
    
    finalizeStatement(stmt);
    return student;
}

std::vector<Student> Database::getAllStudents() {
    std::vector<Student> students;
    std::string sql = R"(
        SELECT id, first_name, last_name, grade_level, student_class, email, phone_number, gpa
        FROM students ORDER BY last_name, first_name;
    )";
    
    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return students;
    
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        Student student(
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0)), // id
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1)), // first_name
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2)), // last_name
            sqlite3_column_int(stmt, 3), // grade_level
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 4)), // student_class
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 5)), // email
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 6))  // phone_number
        );
        student.setGPA(sqlite3_column_double(stmt, 7));
        
        // Load enrolled courses
        auto courses = getStudentCourses(student.getId());
        for (const auto& courseId : courses) {
            student.enrollInCourse(courseId);
        }
        
        students.push_back(student);
    }
    
    finalizeStatement(stmt);
    return students;
}

// Teacher operations
bool Database::insertTeacher(const Teacher& teacher) {
    std::string sql = R"(
        INSERT INTO teachers (id, first_name, last_name, department, email, phone_number, salary)
        VALUES (?, ?, ?, ?, ?, ?, ?);
    )";

    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return false;

    sqlite3_bind_text(stmt, 1, teacher.getId().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, teacher.getFirstName().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 3, teacher.getLastName().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 4, teacher.getDepartment().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 5, teacher.getEmail().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 6, teacher.getPhoneNumber().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_double(stmt, 7, teacher.getSalary());

    int result = sqlite3_step(stmt);
    finalizeStatement(stmt);

    if (result != SQLITE_DONE) {
        std::cerr << "Failed to insert teacher: " << sqlite3_errmsg(db) << std::endl;
        return false;
    }

    // Insert subjects
    for (const auto& subject : teacher.getSubjects()) {
        std::string subjectSql = "INSERT OR IGNORE INTO teacher_subjects (teacher_id, subject) VALUES (?, ?);";
        sqlite3_stmt* subjectStmt = prepareStatement(subjectSql);
        if (subjectStmt) {
            sqlite3_bind_text(subjectStmt, 1, teacher.getId().c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_text(subjectStmt, 2, subject.c_str(), -1, SQLITE_STATIC);
            sqlite3_step(subjectStmt);
            finalizeStatement(subjectStmt);
        }
    }

    return true;
}

Teacher Database::getTeacher(const std::string& teacherId) {
    std::string sql = R"(
        SELECT id, first_name, last_name, department, email, phone_number, salary
        FROM teachers WHERE id = ?;
    )";

    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return Teacher();

    sqlite3_bind_text(stmt, 1, teacherId.c_str(), -1, SQLITE_STATIC);

    Teacher teacher;
    if (sqlite3_step(stmt) == SQLITE_ROW) {
        teacher = Teacher(
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0)), // id
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1)), // first_name
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2)), // last_name
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 3)), // department
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 4)), // email
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 5))  // phone_number
        );
        teacher.setSalary(sqlite3_column_double(stmt, 6));

        // Load subjects
        std::string subjectSql = "SELECT subject FROM teacher_subjects WHERE teacher_id = ?;";
        sqlite3_stmt* subjectStmt = prepareStatement(subjectSql);
        if (subjectStmt) {
            sqlite3_bind_text(subjectStmt, 1, teacherId.c_str(), -1, SQLITE_STATIC);
            while (sqlite3_step(subjectStmt) == SQLITE_ROW) {
                teacher.addSubject(reinterpret_cast<const char*>(sqlite3_column_text(subjectStmt, 0)));
            }
            finalizeStatement(subjectStmt);
        }

        // Load assigned courses
        auto courses = getTeacherCourses(teacherId);
        for (const auto& courseId : courses) {
            teacher.assignCourse(courseId);
        }
    }

    finalizeStatement(stmt);
    return teacher;
}

std::vector<Teacher> Database::getAllTeachers() {
    std::vector<Teacher> teachers;
    std::string sql = R"(
        SELECT id, first_name, last_name, department, email, phone_number, salary
        FROM teachers ORDER BY last_name, first_name;
    )";

    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return teachers;

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        Teacher teacher(
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0)), // id
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1)), // first_name
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2)), // last_name
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 3)), // department
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 4)), // email
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 5))  // phone_number
        );
        teacher.setSalary(sqlite3_column_double(stmt, 6));

        // Load subjects
        std::string subjectSql = "SELECT subject FROM teacher_subjects WHERE teacher_id = ?;";
        sqlite3_stmt* subjectStmt = prepareStatement(subjectSql);
        if (subjectStmt) {
            sqlite3_bind_text(subjectStmt, 1, teacher.getId().c_str(), -1, SQLITE_STATIC);
            while (sqlite3_step(subjectStmt) == SQLITE_ROW) {
                teacher.addSubject(reinterpret_cast<const char*>(sqlite3_column_text(subjectStmt, 0)));
            }
            finalizeStatement(subjectStmt);
        }

        // Load assigned courses
        auto courses = getTeacherCourses(teacher.getId());
        for (const auto& courseId : courses) {
            teacher.assignCourse(courseId);
        }

        teachers.push_back(teacher);
    }

    finalizeStatement(stmt);
    return teachers;
}

// Course operations
bool Database::insertCourse(const Course& course) {
    std::string sql = R"(
        INSERT INTO courses (id, name, credits, max_capacity, teacher_id, description, schedule)
        VALUES (?, ?, ?, ?, ?, ?, ?);
    )";

    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return false;

    sqlite3_bind_text(stmt, 1, course.getCourseId().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, course.getCourseName().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 3, course.getCredits());
    sqlite3_bind_int(stmt, 4, course.getMaxCapacity());
    sqlite3_bind_text(stmt, 5, course.getTeacherId().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 6, course.getDescription().c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 7, course.getSchedule().c_str(), -1, SQLITE_STATIC);

    int result = sqlite3_step(stmt);
    finalizeStatement(stmt);

    if (result != SQLITE_DONE) {
        std::cerr << "Failed to insert course: " << sqlite3_errmsg(db) << std::endl;
        return false;
    }

    // Insert enrolled students
    for (const auto& studentId : course.getEnrolledStudents()) {
        enrollStudentInCourse(studentId, course.getCourseId());
    }

    return true;
}

// Enrollment operations
bool Database::enrollStudentInCourse(const std::string& studentId, const std::string& courseId) {
    std::string sql = "INSERT OR IGNORE INTO enrollments (student_id, course_id) VALUES (?, ?);";
    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return false;

    sqlite3_bind_text(stmt, 1, studentId.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, courseId.c_str(), -1, SQLITE_STATIC);

    int result = sqlite3_step(stmt);
    finalizeStatement(stmt);

    return result == SQLITE_DONE;
}

std::vector<std::string> Database::getStudentCourses(const std::string& studentId) {
    std::vector<std::string> courses;
    std::string sql = "SELECT course_id FROM enrollments WHERE student_id = ?;";

    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return courses;

    sqlite3_bind_text(stmt, 1, studentId.c_str(), -1, SQLITE_STATIC);

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        courses.push_back(reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0)));
    }

    finalizeStatement(stmt);
    return courses;
}

std::vector<std::string> Database::getCourseStudents(const std::string& courseId) {
    std::vector<std::string> students;
    std::string sql = "SELECT student_id FROM enrollments WHERE course_id = ?;";

    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return students;

    sqlite3_bind_text(stmt, 1, courseId.c_str(), -1, SQLITE_STATIC);

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        students.push_back(reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0)));
    }

    finalizeStatement(stmt);
    return students;
}

std::vector<std::string> Database::getTeacherCourses(const std::string& teacherId) {
    std::vector<std::string> courses;
    std::string sql = "SELECT id FROM courses WHERE teacher_id = ?;";

    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return courses;

    sqlite3_bind_text(stmt, 1, teacherId.c_str(), -1, SQLITE_STATIC);

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        courses.push_back(reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0)));
    }

    finalizeStatement(stmt);
    return courses;
}

// Statistics
int Database::getStudentCount() {
    std::string sql = "SELECT COUNT(*) FROM students;";
    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return 0;

    int count = 0;
    if (sqlite3_step(stmt) == SQLITE_ROW) {
        count = sqlite3_column_int(stmt, 0);
    }

    finalizeStatement(stmt);
    return count;
}

int Database::getTeacherCount() {
    std::string sql = "SELECT COUNT(*) FROM teachers;";
    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return 0;

    int count = 0;
    if (sqlite3_step(stmt) == SQLITE_ROW) {
        count = sqlite3_column_int(stmt, 0);
    }

    finalizeStatement(stmt);
    return count;
}

int Database::getCourseCount() {
    std::string sql = "SELECT COUNT(*) FROM courses;";
    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return 0;

    int count = 0;
    if (sqlite3_step(stmt) == SQLITE_ROW) {
        count = sqlite3_column_int(stmt, 0);
    }

    finalizeStatement(stmt);
    return count;
}

int Database::getGradeCount() {
    std::string sql = "SELECT COUNT(*) FROM grades;";
    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return 0;

    int count = 0;
    if (sqlite3_step(stmt) == SQLITE_ROW) {
        count = sqlite3_column_int(stmt, 0);
    }

    finalizeStatement(stmt);
    return count;
}

bool Database::generateTestData() {
    std::cout << "Generating test data..." << std::endl;

    // Clear existing data first
    clearAllData();

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> gradeDist(1, 12);
    std::uniform_int_distribution<> scoreDist(60, 100);
    std::uniform_real_distribution<> salaryDist(30000, 80000);

    // Sample data arrays
    std::vector<std::string> firstNames = {
        "Ahmed", "Sarah", "Mohammed", "Fatima", "Ali", "Aisha", "Omar", "Zainab",
        "Hassan", "Maryam", "Youssef", "Nour", "Khaled", "Layla", "Amr", "Dina",
        "Mahmoud", "Hala", "Tarek", "Rana", "Sami", "Lina", "Fadi", "Maya",
        "Karim", "Reem", "Nader", "Salma", "Walid", "Yasmin"
    };

    std::vector<std::string> lastNames = {
        "Ahmed", "Hassan", "Ali", "Mohammed", "Ibrahim", "Mahmoud", "Youssef",
        "Omar", "Khaled", "Sami", "Nader", "Tarek", "Amr", "Fadi", "Karim",
        "Walid", "Saeed", "Farid", "Rashid", "Majid", "Adel", "Nabil", "Salim",
        "Hakim", "Jamil", "Latif", "Munir", "Nasir", "Qasim", "Rafiq"
    };

    std::vector<std::string> departments = {
        "Mathematics", "Science", "English", "Arabic", "History", "Geography",
        "Physics", "Chemistry", "Biology", "Computer Science", "Art", "Music",
        "Physical Education", "Islamic Studies", "Social Studies"
    };

    std::vector<std::string> subjects = {
        "Algebra", "Geometry", "Calculus", "Physics", "Chemistry", "Biology",
        "English Literature", "Arabic Grammar", "World History", "Geography",
        "Computer Programming", "Art History", "Music Theory", "Physical Fitness"
    };

    std::vector<std::string> courseNames = {
        "Advanced Mathematics", "General Science", "English Composition", "Arabic Literature",
        "World History", "Physical Geography", "Physics Lab", "Chemistry Fundamentals",
        "Biology Basics", "Computer Programming", "Art Appreciation", "Music Fundamentals",
        "Physical Education", "Islamic History", "Social Studies", "Environmental Science",
        "Statistics", "Creative Writing", "Public Speaking", "Research Methods"
    };

    // Generate 50 students
    std::cout << "Creating students..." << std::endl;
    for (int i = 1; i <= 50; i++) {
        std::string id = "S" + std::to_string(1000 + i);
        std::string firstName = firstNames[gen() % firstNames.size()];
        std::string lastName = lastNames[gen() % lastNames.size()];
        int gradeLevel = gradeDist(gen);
        std::string studentClass = std::to_string(gradeLevel) + "A";
        std::string email = firstName + "." + lastName + "@school.edu";
        std::string phone = "05" + std::to_string(10000000 + (gen() % 90000000));

        Student student(id, firstName, lastName, gradeLevel, studentClass, email, phone);
        insertStudent(student);
    }

    // Generate 20 teachers
    std::cout << "Creating teachers..." << std::endl;
    for (int i = 1; i <= 20; i++) {
        std::string id = "T" + std::to_string(100 + i);
        std::string firstName = firstNames[gen() % firstNames.size()];
        std::string lastName = lastNames[gen() % lastNames.size()];
        std::string department = departments[gen() % departments.size()];
        std::string email = firstName + "." + lastName + "@school.edu";
        std::string phone = "05" + std::to_string(10000000 + (gen() % 90000000));

        Teacher teacher(id, firstName, lastName, department, email, phone);
        teacher.setSalary(salaryDist(gen));

        // Add 2-3 subjects per teacher
        int numSubjects = 2 + (gen() % 2);
        for (int j = 0; j < numSubjects; j++) {
            teacher.addSubject(subjects[gen() % subjects.size()]);
        }

        insertTeacher(teacher);
    }

    // Generate 20 courses
    std::cout << "Creating courses..." << std::endl;
    auto teachers = getAllTeachers();
    for (int i = 1; i <= 20; i++) {
        std::string id = "C" + std::to_string(100 + i);
        std::string name = courseNames[gen() % courseNames.size()];
        int credits = 3 + (gen() % 3); // 3-5 credits
        int maxCapacity = 20 + (gen() % 21); // 20-40 students
        std::string teacherId = teachers[gen() % teachers.size()].getId();
        std::string description = "Course description for " + name;
        std::string schedule = "MWF 10:00-11:00";

        Course course(id, name, credits, maxCapacity, teacherId, description, schedule);
        insertCourse(course);
    }

    // Enroll students in courses (each student in 3-5 courses)
    std::cout << "Creating enrollments..." << std::endl;
    auto students = getAllStudents();
    auto courses = getAllCourses();

    for (const auto& student : students) {
        int numCourses = 3 + (gen() % 3); // 3-5 courses per student
        std::vector<int> selectedCourses;

        for (int i = 0; i < numCourses && i < courses.size(); i++) {
            int courseIndex;
            do {
                courseIndex = gen() % courses.size();
            } while (std::find(selectedCourses.begin(), selectedCourses.end(), courseIndex) != selectedCourses.end());

            selectedCourses.push_back(courseIndex);
            enrollStudentInCourse(student.getId(), courses[courseIndex].getCourseId());
        }
    }

    std::cout << "Test data generation completed!" << std::endl;
    std::cout << "Generated: " << getStudentCount() << " students, "
              << getTeacherCount() << " teachers, "
              << getCourseCount() << " courses" << std::endl;

    return true;
}

std::vector<Course> Database::getAllCourses() {
    std::vector<Course> courses;
    std::string sql = R"(
        SELECT id, name, credits, max_capacity, teacher_id, description, schedule
        FROM courses ORDER BY name;
    )";

    sqlite3_stmt* stmt = prepareStatement(sql);
    if (!stmt) return courses;

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        Course course(
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0)), // id
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1)), // name
            sqlite3_column_int(stmt, 2), // credits
            sqlite3_column_int(stmt, 3), // max_capacity
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 4)), // teacher_id
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 5)), // description
            reinterpret_cast<const char*>(sqlite3_column_text(stmt, 6))  // schedule
        );

        // Load enrolled students
        auto students = getCourseStudents(course.getCourseId());
        for (const auto& studentId : students) {
            course.enrollStudent(studentId);
        }

        courses.push_back(course);
    }

    finalizeStatement(stmt);
    return courses;
}

bool Database::clearAllData() {
    return executeSQL("DELETE FROM grades;") &&
           executeSQL("DELETE FROM enrollments;") &&
           executeSQL("DELETE FROM teacher_subjects;") &&
           executeSQL("DELETE FROM courses;") &&
           executeSQL("DELETE FROM teachers;") &&
           executeSQL("DELETE FROM students;");
}

bool Database::isConnected() const {
    return db != nullptr;
}

std::string Database::getLastError() const {
    if (db) {
        return std::string(sqlite3_errmsg(db));
    }
    return "Database not connected";
}
