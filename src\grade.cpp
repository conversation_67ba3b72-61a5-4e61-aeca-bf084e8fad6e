#include "grade.h"
#include <algorithm>
#include <iomanip>
#include <ctime>

// Default constructor
Grade::Grade() : studentId(""), courseId(""), score(0.0), gradeType(""), weight(1.0) {
    // Set current date
    time_t now = time(0);
    char* dt = ctime(&now);
    dateRecorded = std::string(dt).substr(0, 24); // Remove newline
}

// Parameterized constructor
Grade::Grade(const std::string& studentId, const std::string& courseId,
             double score, const std::string& gradeType, double weight)
    : studentId(studentId), courseId(courseId), score(score), 
      gradeType(gradeType), weight(weight) {
    // Set current date
    time_t now = time(0);
    char* dt = ctime(&now);
    dateRecorded = std::string(dt).substr(0, 24); // Remove newline
}

// Grade calculation functions
char Grade::getLetterGrade() const {
    return convertToLetterGrade(score);
}

double Grade::getGradePoints() const {
    return convertToGradePoints(getLetterGrade());
}

bool Grade::isPassingGrade() const {
    return score >= 60.0; // Assuming 60% is passing
}

// Utility functions
void Grade::displayInfo() const {
    std::cout << "\n=== Grade Information ===" << std::endl;
    std::cout << "Student ID: " << studentId << std::endl;
    std::cout << "Course ID: " << courseId << std::endl;
    std::cout << "Grade Type: " << gradeType << std::endl;
    std::cout << "Score: " << std::fixed << std::setprecision(1) << score << "%" << std::endl;
    std::cout << "Letter Grade: " << getLetterGrade() << std::endl;
    std::cout << "Grade Points: " << std::fixed << std::setprecision(2) << getGradePoints() << std::endl;
    std::cout << "Weight: " << weight << std::endl;
    std::cout << "Date Recorded: " << dateRecorded << std::endl;
    std::cout << "Status: " << (isPassingGrade() ? "PASS" : "FAIL") << std::endl;
    std::cout << "=========================" << std::endl;
}

bool Grade::isValid() const {
    return !studentId.empty() && !courseId.empty() && !gradeType.empty() &&
           isValidScore(score) && weight > 0.0;
}

// Static utility functions
char Grade::convertToLetterGrade(double score) {
    if (score >= 90.0) return 'A';
    else if (score >= 80.0) return 'B';
    else if (score >= 70.0) return 'C';
    else if (score >= 60.0) return 'D';
    else return 'F';
}

double Grade::convertToGradePoints(char letterGrade) {
    switch (letterGrade) {
        case 'A': return 4.0;
        case 'B': return 3.0;
        case 'C': return 2.0;
        case 'D': return 1.0;
        case 'F': return 0.0;
        default: return 0.0;
    }
}

bool Grade::isValidScore(double score) {
    return score >= 0.0 && score <= 100.0;
}

// Operators
bool Grade::operator==(const Grade& other) const {
    return studentId == other.studentId && courseId == other.courseId && 
           gradeType == other.gradeType;
}

std::ostream& operator<<(std::ostream& os, const Grade& grade) {
    os << "Grade[Student: " << grade.studentId << ", Course: " << grade.courseId 
       << ", Type: " << grade.gradeType << ", Score: " 
       << std::fixed << std::setprecision(1) << grade.score << "% (" 
       << grade.getLetterGrade() << ")]";
    return os;
}

// GradeManager implementation
bool GradeManager::addGrade(const Grade& grade) {
    if (!grade.isValid()) {
        return false;
    }
    
    // Check if grade already exists
    Grade* existing = findGrade(grade.getStudentId(), grade.getCourseId(), grade.getGradeType());
    if (existing != nullptr) {
        // Update existing grade
        *existing = grade;
    } else {
        // Add new grade
        grades.push_back(grade);
    }
    return true;
}

bool GradeManager::updateGrade(const std::string& studentId, const std::string& courseId,
                              const std::string& gradeType, double newScore) {
    Grade* grade = findGrade(studentId, courseId, gradeType);
    if (grade != nullptr && Grade::isValidScore(newScore)) {
        grade->setScore(newScore);
        return true;
    }
    return false;
}

bool GradeManager::removeGrade(const std::string& studentId, const std::string& courseId,
                              const std::string& gradeType) {
    auto it = std::find_if(grades.begin(), grades.end(),
        [&](const Grade& g) {
            return g.getStudentId() == studentId && g.getCourseId() == courseId && 
                   g.getGradeType() == gradeType;
        });
    
    if (it != grades.end()) {
        grades.erase(it);
        return true;
    }
    return false;
}

std::vector<Grade> GradeManager::getStudentGrades(const std::string& studentId) const {
    std::vector<Grade> studentGrades;
    for (const auto& grade : grades) {
        if (grade.getStudentId() == studentId) {
            studentGrades.push_back(grade);
        }
    }
    return studentGrades;
}

std::vector<Grade> GradeManager::getCourseGrades(const std::string& courseId) const {
    std::vector<Grade> courseGrades;
    for (const auto& grade : grades) {
        if (grade.getCourseId() == courseId) {
            courseGrades.push_back(grade);
        }
    }
    return courseGrades;
}

Grade* GradeManager::findGrade(const std::string& studentId, const std::string& courseId,
                               const std::string& gradeType) {
    auto it = std::find_if(grades.begin(), grades.end(),
        [&](const Grade& g) {
            return g.getStudentId() == studentId && g.getCourseId() == courseId && 
                   g.getGradeType() == gradeType;
        });
    
    return (it != grades.end()) ? &(*it) : nullptr;
}

double GradeManager::calculateCourseGPA(const std::string& studentId, const std::string& courseId) const {
    std::vector<Grade> courseGrades;
    double totalWeightedScore = 0.0;
    double totalWeight = 0.0;
    
    for (const auto& grade : grades) {
        if (grade.getStudentId() == studentId && grade.getCourseId() == courseId) {
            totalWeightedScore += grade.getScore() * grade.getWeight();
            totalWeight += grade.getWeight();
        }
    }
    
    return totalWeight > 0 ? totalWeightedScore / totalWeight : 0.0;
}

double GradeManager::calculateOverallGPA(const std::string& studentId) const {
    std::map<std::string, double> courseGPAs;
    
    // Get all courses for student
    std::vector<std::string> studentCourses;
    for (const auto& grade : grades) {
        if (grade.getStudentId() == studentId) {
            if (std::find(studentCourses.begin(), studentCourses.end(), grade.getCourseId()) 
                == studentCourses.end()) {
                studentCourses.push_back(grade.getCourseId());
            }
        }
    }
    
    // Calculate GPA for each course
    double totalGPA = 0.0;
    int courseCount = 0;
    
    for (const std::string& courseId : studentCourses) {
        double courseGPA = calculateCourseGPA(studentId, courseId);
        if (courseGPA > 0) {
            totalGPA += Grade::convertToGradePoints(Grade::convertToLetterGrade(courseGPA));
            courseCount++;
        }
    }
    
    return courseCount > 0 ? totalGPA / courseCount : 0.0;
}

double GradeManager::getCourseAverage(const std::string& courseId) const {
    std::vector<Grade> courseGrades = getCourseGrades(courseId);
    if (courseGrades.empty()) return 0.0;
    
    double total = 0.0;
    for (const auto& grade : courseGrades) {
        total += grade.getScore();
    }
    
    return total / courseGrades.size();
}

std::map<char, int> GradeManager::getGradeDistribution(const std::string& courseId) const {
    std::map<char, int> distribution = {{'A', 0}, {'B', 0}, {'C', 0}, {'D', 0}, {'F', 0}};
    
    std::vector<Grade> courseGrades = getCourseGrades(courseId);
    for (const auto& grade : courseGrades) {
        distribution[grade.getLetterGrade()]++;
    }
    
    return distribution;
}

void GradeManager::displayAllGrades() const {
    std::cout << "\n=== All Grades ===" << std::endl;
    std::cout << "Total Grades: " << grades.size() << std::endl;
    
    if (grades.empty()) {
        std::cout << "No grades recorded." << std::endl;
    } else {
        for (const auto& grade : grades) {
            std::cout << grade << std::endl;
        }
    }
    std::cout << "==================" << std::endl;
}

void GradeManager::displayStudentGrades(const std::string& studentId) const {
    std::vector<Grade> studentGrades = getStudentGrades(studentId);
    
    std::cout << "\n=== Student Grades ===" << std::endl;
    std::cout << "Student ID: " << studentId << std::endl;
    std::cout << "Total Grades: " << studentGrades.size() << std::endl;
    std::cout << "Overall GPA: " << std::fixed << std::setprecision(2) 
              << calculateOverallGPA(studentId) << std::endl;
    
    if (studentGrades.empty()) {
        std::cout << "No grades recorded for this student." << std::endl;
    } else {
        for (const auto& grade : studentGrades) {
            std::cout << grade << std::endl;
        }
    }
    std::cout << "======================" << std::endl;
}

void GradeManager::displayCourseGrades(const std::string& courseId) const {
    std::vector<Grade> courseGrades = getCourseGrades(courseId);
    
    std::cout << "\n=== Course Grades ===" << std::endl;
    std::cout << "Course ID: " << courseId << std::endl;
    std::cout << "Total Grades: " << courseGrades.size() << std::endl;
    std::cout << "Course Average: " << std::fixed << std::setprecision(2) 
              << getCourseAverage(courseId) << "%" << std::endl;
    
    if (courseGrades.empty()) {
        std::cout << "No grades recorded for this course." << std::endl;
    } else {
        for (const auto& grade : courseGrades) {
            std::cout << grade << std::endl;
        }
        
        // Display grade distribution
        auto distribution = getGradeDistribution(courseId);
        std::cout << "\nGrade Distribution:" << std::endl;
        for (const auto& pair : distribution) {
            std::cout << pair.first << ": " << pair.second << " students" << std::endl;
        }
    }
    std::cout << "=====================" << std::endl;
}
