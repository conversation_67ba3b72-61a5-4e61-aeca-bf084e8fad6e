#include <iostream>
#include <vector>
#include <string>
#include <limits>
#include <algorithm>
#include <iomanip>
#include "student.h"
#include "teacher.h"
#include "course.h"
#include "grade.h"
#include "database.h"

class SchoolManagementSystem {
private:
    Database database;
    GradeManager gradeManager;

public:
    SchoolManagementSystem() : database("school_management.db") {}

    bool initialize();
    void run();
    void displayMainMenu();
    void handleStudentMenu();
    void handleTeacherMenu();
    void handleCourseMenu();
    void handleGradeMenu();
    void handleReportsMenu();
    void handleTestDataMenu();

private:
    // Student management
    void addStudent();
    void searchStudent();
    void updateStudent();
    void deleteStudent();
    void listAllStudents();
    // Database-based student operations - findStudent no longer needed

    // Teacher management
    void addTeacher();
    void searchTeacher();
    void updateTeacher();
    void deleteTeacher();
    void listAllTeachers();
    Teacher* findTeacher(const std::string& id);

    // Course management
    void addCourse();
    void searchCourse();
    void updateCourse();
    void deleteCourse();
    void listAllCourses();
    void enrollStudentInCourse();
    void dropStudentFromCourse();
    Course* findCourse(const std::string& id);

    // Grade management
    void recordGrade();
    void updateGrade();
    void deleteGrade();
    void viewStudentGrades();
    void viewCourseGrades();

    // Reports
    void generateStudentReport();
    void generateCourseReport();
    void generateSystemStatistics();

    // Utility functions
    void clearScreen();
    void pauseScreen();
    int getMenuChoice(int minChoice, int maxChoice);
    void displayHeader(const std::string& title);
    bool confirmAction(const std::string& message);
};

int main() {
    std::cout << "=== School Management System ===" << std::endl;
    std::cout << "Welcome to the School Management System!" << std::endl;
    std::cout << "=================================" << std::endl;

    SchoolManagementSystem system;

    if (!system.initialize()) {
        std::cerr << "Failed to initialize system. Exiting..." << std::endl;
        return 1;
    }

    system.run();

    std::cout << "\nThank you for using School Management System!" << std::endl;
    return 0;
}

bool SchoolManagementSystem::initialize() {
    std::cout << "Initializing School Management System..." << std::endl;

    if (!database.initialize()) {
        std::cerr << "Failed to initialize database: " << database.getLastError() << std::endl;
        return false;
    }

    std::cout << "Database initialized successfully!" << std::endl;
    return true;
}

void SchoolManagementSystem::run() {
    int choice;
    do {
        displayMainMenu();
        choice = getMenuChoice(0, 7);

        switch (choice) {
            case 1: handleStudentMenu(); break;
            case 2: handleTeacherMenu(); break;
            case 3: handleCourseMenu(); break;
            case 4: handleGradeMenu(); break;
            case 5: handleReportsMenu(); break;
            case 6:
                std::cout << "\nSystem Statistics:" << std::endl;
                std::cout << "Students: " << database.getStudentCount() << std::endl;
                std::cout << "Teachers: " << database.getTeacherCount() << std::endl;
                std::cout << "Courses: " << database.getCourseCount() << std::endl;
                std::cout << "Grades: " << database.getGradeCount() << std::endl;
                pauseScreen();
                break;
            case 7: handleTestDataMenu(); break;
            case 0:
                if (confirmAction("Are you sure you want to exit?")) {
                    std::cout << "Exiting system..." << std::endl;
                } else {
                    choice = -1; // Continue running
                }
                break;
        }
    } while (choice != 0);
}

void SchoolManagementSystem::displayMainMenu() {
    clearScreen();
    displayHeader("MAIN MENU");
    std::cout << "1. Student Management" << std::endl;
    std::cout << "2. Teacher Management" << std::endl;
    std::cout << "3. Course Management" << std::endl;
    std::cout << "4. Grade Management" << std::endl;
    std::cout << "5. Reports" << std::endl;
    std::cout << "6. System Statistics" << std::endl;
    std::cout << "7. Test Data Management" << std::endl;
    std::cout << "0. Exit" << std::endl;
    std::cout << "\nEnter your choice: ";
}

void SchoolManagementSystem::handleStudentMenu() {
    int choice;
    do {
        clearScreen();
        displayHeader("STUDENT MANAGEMENT");
        std::cout << "1. Add New Student" << std::endl;
        std::cout << "2. Search Student" << std::endl;
        std::cout << "3. Update Student" << std::endl;
        std::cout << "4. Delete Student" << std::endl;
        std::cout << "5. List All Students" << std::endl;
        std::cout << "0. Back to Main Menu" << std::endl;
        std::cout << "\nEnter your choice: ";
        
        choice = getMenuChoice(0, 5);
        
        switch (choice) {
            case 1: addStudent(); break;
            case 2: searchStudent(); break;
            case 3: updateStudent(); break;
            case 4: deleteStudent(); break;
            case 5: listAllStudents(); break;
        }
    } while (choice != 0);
}

void SchoolManagementSystem::addStudent() {
    clearScreen();
    displayHeader("ADD NEW STUDENT");
    
    std::string id, firstName, lastName, studentClass, email, phone;
    int gradeLevel;
    
    std::cout << "Enter Student ID: ";
    std::cin >> id;
    
    // Check if student already exists
    Student existingStudent = database.getStudent(id);
    if (!existingStudent.getId().empty()) {
        std::cout << "Error: Student with ID " << id << " already exists!" << std::endl;
        pauseScreen();
        return;
    }
    
    std::cout << "Enter First Name: ";
    std::cin >> firstName;
    std::cout << "Enter Last Name: ";
    std::cin >> lastName;
    
    do {
        std::cout << "Enter Grade Level (1-12): ";
        std::cin >> gradeLevel;
        if (gradeLevel < 1 || gradeLevel > 12) {
            std::cout << "Invalid grade level! Please enter a value between 1 and 12." << std::endl;
        }
    } while (gradeLevel < 1 || gradeLevel > 12);
    
    std::cout << "Enter Class (optional): ";
    std::cin.ignore();
    std::getline(std::cin, studentClass);
    
    std::cout << "Enter Email (optional): ";
    std::getline(std::cin, email);
    
    std::cout << "Enter Phone (optional): ";
    std::getline(std::cin, phone);
    
    Student newStudent(id, firstName, lastName, gradeLevel, studentClass, email, phone);

    if (database.insertStudent(newStudent)) {
        std::cout << "\nStudent added successfully!" << std::endl;
        newStudent.displayInfo();
    } else {
        std::cout << "\nError: Failed to add student to database!" << std::endl;
    }

    pauseScreen();
}

void SchoolManagementSystem::searchStudent() {
    clearScreen();
    displayHeader("SEARCH STUDENT");

    std::string id;
    std::cout << "Enter Student ID to search: ";
    std::cin >> id;

    Student student = database.getStudent(id);
    if (!student.getId().empty()) {
        student.displayInfo();
    } else {
        std::cout << "Student with ID " << id << " not found!" << std::endl;
    }

    pauseScreen();
}

void SchoolManagementSystem::listAllStudents() {
    clearScreen();
    displayHeader("ALL STUDENTS");

    auto students = database.getAllStudents();

    if (students.empty()) {
        std::cout << "No students registered in the system." << std::endl;
    } else {
        std::cout << "Total Students: " << students.size() << std::endl;
        std::cout << std::string(80, '-') << std::endl;

        for (const auto& student : students) {
            std::cout << student << std::endl;
        }
    }

    pauseScreen();
}

// Database-based student operations - findStudent no longer needed

// Utility functions
void SchoolManagementSystem::clearScreen() {
#ifdef _WIN32
    system("cls");
#else
    system("clear");
#endif
}

void SchoolManagementSystem::pauseScreen() {
    std::cout << "\nPress Enter to continue...";
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    std::cin.get();
}

int SchoolManagementSystem::getMenuChoice(int minChoice, int maxChoice) {
    int choice;
    while (true) {
        std::cin >> choice;
        if (std::cin.fail() || choice < minChoice || choice > maxChoice) {
            std::cin.clear();
            std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
            std::cout << "Invalid choice! Please enter a number between " 
                      << minChoice << " and " << maxChoice << ": ";
        } else {
            std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
            return choice;
        }
    }
}

void SchoolManagementSystem::displayHeader(const std::string& title) {
    std::cout << std::string(50, '=') << std::endl;
    std::cout << "  " << title << std::endl;
    std::cout << std::string(50, '=') << std::endl;
}

bool SchoolManagementSystem::confirmAction(const std::string& message) {
    char choice;
    std::cout << message << " (y/n): ";
    std::cin >> choice;
    return (choice == 'y' || choice == 'Y');
}

// Teacher Management Implementation
void SchoolManagementSystem::handleTeacherMenu() {
    int choice;
    do {
        clearScreen();
        displayHeader("TEACHER MANAGEMENT");
        std::cout << "1. Add New Teacher" << std::endl;
        std::cout << "2. Search Teacher" << std::endl;
        std::cout << "3. List All Teachers" << std::endl;
        std::cout << "0. Back to Main Menu" << std::endl;
        std::cout << "\nEnter your choice: ";

        choice = getMenuChoice(0, 3);

        switch (choice) {
            case 1: addTeacher(); break;
            case 2: searchTeacher(); break;
            case 3: listAllTeachers(); break;
        }
    } while (choice != 0);
}

void SchoolManagementSystem::addTeacher() {
    clearScreen();
    displayHeader("ADD NEW TEACHER");

    std::string id, firstName, lastName, department, email, phone;

    std::cout << "Enter Teacher ID: ";
    std::cin >> id;

    if (findTeacher(id) != nullptr) {
        std::cout << "Error: Teacher with ID " << id << " already exists!" << std::endl;
        pauseScreen();
        return;
    }

    std::cout << "Enter First Name: ";
    std::cin >> firstName;
    std::cout << "Enter Last Name: ";
    std::cin >> lastName;
    std::cout << "Enter Department: ";
    std::cin >> department;

    Teacher newTeacher(id, firstName, lastName, department);

    std::cout << "Enter Email (optional): ";
    std::cin.ignore();
    std::getline(std::cin, email);
    newTeacher.setEmail(email);

    std::cout << "Enter Phone (optional): ";
    std::getline(std::cin, phone);
    newTeacher.setPhoneNumber(phone);

    teachers.push_back(newTeacher);

    std::cout << "\nTeacher added successfully!" << std::endl;
    newTeacher.displayInfo();
    pauseScreen();
}

void SchoolManagementSystem::searchTeacher() {
    clearScreen();
    displayHeader("SEARCH TEACHER");

    std::string id;
    std::cout << "Enter Teacher ID to search: ";
    std::cin >> id;

    Teacher* teacher = findTeacher(id);
    if (teacher != nullptr) {
        teacher->displayInfo();
    } else {
        std::cout << "Teacher with ID " << id << " not found!" << std::endl;
    }

    pauseScreen();
}

void SchoolManagementSystem::listAllTeachers() {
    clearScreen();
    displayHeader("ALL TEACHERS");

    auto teachers = database.getAllTeachers();

    if (teachers.empty()) {
        std::cout << "No teachers registered in the system." << std::endl;
    } else {
        std::cout << "Total Teachers: " << teachers.size() << std::endl;
        std::cout << std::string(80, '-') << std::endl;

        for (const auto& teacher : teachers) {
            std::cout << teacher << std::endl;
        }
    }

    pauseScreen();
}

Teacher* SchoolManagementSystem::findTeacher(const std::string& id) {
    auto it = std::find_if(teachers.begin(), teachers.end(),
        [&id](const Teacher& t) { return t.getId() == id; });
    return (it != teachers.end()) ? &(*it) : nullptr;
}

// Course Management Implementation
void SchoolManagementSystem::handleCourseMenu() {
    int choice;
    do {
        clearScreen();
        displayHeader("COURSE MANAGEMENT");
        std::cout << "1. Add New Course" << std::endl;
        std::cout << "2. Search Course" << std::endl;
        std::cout << "3. List All Courses" << std::endl;
        std::cout << "4. Enroll Student in Course" << std::endl;
        std::cout << "5. Drop Student from Course" << std::endl;
        std::cout << "0. Back to Main Menu" << std::endl;
        std::cout << "\nEnter your choice: ";

        choice = getMenuChoice(0, 5);

        switch (choice) {
            case 1: addCourse(); break;
            case 2: searchCourse(); break;
            case 3: listAllCourses(); break;
            case 4: enrollStudentInCourse(); break;
            case 5: dropStudentFromCourse(); break;
        }
    } while (choice != 0);
}

void SchoolManagementSystem::addCourse() {
    clearScreen();
    displayHeader("ADD NEW COURSE");

    std::string courseId, courseName, description, teacherId;
    int credits, maxCapacity;

    std::cout << "Enter Course ID: ";
    std::cin >> courseId;

    if (findCourse(courseId) != nullptr) {
        std::cout << "Error: Course with ID " << courseId << " already exists!" << std::endl;
        pauseScreen();
        return;
    }

    std::cout << "Enter Course Name: ";
    std::cin.ignore();
    std::getline(std::cin, courseName);

    std::cout << "Enter Credits: ";
    std::cin >> credits;

    std::cout << "Enter Maximum Capacity: ";
    std::cin >> maxCapacity;

    Course newCourse(courseId, courseName, credits, maxCapacity);

    std::cout << "Enter Description (optional): ";
    std::cin.ignore();
    std::getline(std::cin, description);
    newCourse.setDescription(description);

    std::cout << "Enter Teacher ID (optional): ";
    std::getline(std::cin, teacherId);
    if (!teacherId.empty() && findTeacher(teacherId) != nullptr) {
        newCourse.setTeacherId(teacherId);
    }

    courses.push_back(newCourse);

    std::cout << "\nCourse added successfully!" << std::endl;
    newCourse.displayInfo();
    pauseScreen();
}

void SchoolManagementSystem::searchCourse() {
    clearScreen();
    displayHeader("SEARCH COURSE");

    std::string id;
    std::cout << "Enter Course ID to search: ";
    std::cin >> id;

    Course* course = findCourse(id);
    if (course != nullptr) {
        course->displayInfo();
        course->displayEnrolledStudents();
    } else {
        std::cout << "Course with ID " << id << " not found!" << std::endl;
    }

    pauseScreen();
}

Course* SchoolManagementSystem::findCourse(const std::string& id) {
    auto it = std::find_if(courses.begin(), courses.end(),
        [&id](const Course& c) { return c.getCourseId() == id; });
    return (it != courses.end()) ? &(*it) : nullptr;
}

void SchoolManagementSystem::listAllCourses() {
    clearScreen();
    displayHeader("ALL COURSES");

    auto courses = database.getAllCourses();

    if (courses.empty()) {
        std::cout << "No courses registered in the system." << std::endl;
    } else {
        std::cout << "Total Courses: " << courses.size() << std::endl;
        std::cout << std::string(80, '-') << std::endl;

        for (const auto& course : courses) {
            std::cout << course << std::endl;
        }
    }

    pauseScreen();
}

void SchoolManagementSystem::enrollStudentInCourse() {
    clearScreen();
    displayHeader("ENROLL STUDENT IN COURSE");

    std::string studentId, courseId;

    std::cout << "Enter Student ID: ";
    std::cin >> studentId;

    Student* student = findStudent(studentId);
    if (student == nullptr) {
        std::cout << "Error: Student with ID " << studentId << " not found!" << std::endl;
        pauseScreen();
        return;
    }

    std::cout << "Enter Course ID: ";
    std::cin >> courseId;

    Course* course = findCourse(courseId);
    if (course == nullptr) {
        std::cout << "Error: Course with ID " << courseId << " not found!" << std::endl;
        pauseScreen();
        return;
    }

    if (course->isFull()) {
        std::cout << "Error: Course is at maximum capacity!" << std::endl;
        pauseScreen();
        return;
    }

    if (course->isStudentEnrolled(studentId)) {
        std::cout << "Error: Student is already enrolled in this course!" << std::endl;
        pauseScreen();
        return;
    }

    if (course->enrollStudent(studentId) && student->enrollInCourse(courseId)) {
        std::cout << "Student " << student->getFullName()
                  << " enrolled in course " << course->getCourseName()
                  << " successfully!" << std::endl;
    } else {
        std::cout << "Error: Failed to enroll student in course!" << std::endl;
    }

    pauseScreen();
}

// Grade Management Implementation
void SchoolManagementSystem::handleGradeMenu() {
    int choice;
    do {
        clearScreen();
        displayHeader("GRADE MANAGEMENT");
        std::cout << "1. Record Grade" << std::endl;
        std::cout << "2. View Student Grades" << std::endl;
        std::cout << "3. View Course Grades" << std::endl;
        std::cout << "4. View All Grades" << std::endl;
        std::cout << "0. Back to Main Menu" << std::endl;
        std::cout << "\nEnter your choice: ";

        choice = getMenuChoice(0, 4);

        switch (choice) {
            case 1: recordGrade(); break;
            case 2: viewStudentGrades(); break;
            case 3: viewCourseGrades(); break;
            case 4: gradeManager.displayAllGrades(); pauseScreen(); break;
        }
    } while (choice != 0);
}

void SchoolManagementSystem::recordGrade() {
    clearScreen();
    displayHeader("RECORD GRADE");

    std::string studentId, courseId, gradeType;
    double score, weight;

    std::cout << "Enter Student ID: ";
    std::cin >> studentId;

    if (findStudent(studentId) == nullptr) {
        std::cout << "Error: Student with ID " << studentId << " not found!" << std::endl;
        pauseScreen();
        return;
    }

    std::cout << "Enter Course ID: ";
    std::cin >> courseId;

    if (findCourse(courseId) == nullptr) {
        std::cout << "Error: Course with ID " << courseId << " not found!" << std::endl;
        pauseScreen();
        return;
    }

    std::cout << "Enter Grade Type (Quiz/Midterm/Final/Assignment): ";
    std::cin >> gradeType;

    do {
        std::cout << "Enter Score (0-100): ";
        std::cin >> score;
        if (score < 0 || score > 100) {
            std::cout << "Invalid score! Please enter a value between 0 and 100." << std::endl;
        }
    } while (score < 0 || score > 100);

    std::cout << "Enter Weight (default 1.0): ";
    std::cin >> weight;
    if (weight <= 0) weight = 1.0;

    Grade newGrade(studentId, courseId, score, gradeType, weight);

    if (gradeManager.addGrade(newGrade)) {
        std::cout << "\nGrade recorded successfully!" << std::endl;
        newGrade.displayInfo();

        // Update student GPA
        double newGPA = gradeManager.calculateOverallGPA(studentId);
        Student* student = findStudent(studentId);
        if (student != nullptr) {
            student->setGPA(newGPA);
            std::cout << "Student GPA updated to: " << std::fixed << std::setprecision(2) << newGPA << std::endl;
        }
    } else {
        std::cout << "Error: Failed to record grade!" << std::endl;
    }

    pauseScreen();
}

void SchoolManagementSystem::viewStudentGrades() {
    clearScreen();
    displayHeader("VIEW STUDENT GRADES");

    std::string studentId;
    std::cout << "Enter Student ID: ";
    std::cin >> studentId;

    if (findStudent(studentId) == nullptr) {
        std::cout << "Error: Student with ID " << studentId << " not found!" << std::endl;
    } else {
        gradeManager.displayStudentGrades(studentId);
    }

    pauseScreen();
}

void SchoolManagementSystem::viewCourseGrades() {
    clearScreen();
    displayHeader("VIEW COURSE GRADES");

    std::string courseId;
    std::cout << "Enter Course ID: ";
    std::cin >> courseId;

    if (findCourse(courseId) == nullptr) {
        std::cout << "Error: Course with ID " << courseId << " not found!" << std::endl;
    } else {
        gradeManager.displayCourseGrades(courseId);
    }

    pauseScreen();
}

// Reports Implementation
void SchoolManagementSystem::handleReportsMenu() {
    int choice;
    do {
        clearScreen();
        displayHeader("REPORTS");
        std::cout << "1. Student Report" << std::endl;
        std::cout << "2. Course Report" << std::endl;
        std::cout << "3. System Statistics" << std::endl;
        std::cout << "0. Back to Main Menu" << std::endl;
        std::cout << "\nEnter your choice: ";

        choice = getMenuChoice(0, 3);

        switch (choice) {
            case 1: generateStudentReport(); break;
            case 2: generateCourseReport(); break;
            case 3: generateSystemStatistics(); break;
        }
    } while (choice != 0);
}

void SchoolManagementSystem::generateStudentReport() {
    clearScreen();
    displayHeader("STUDENT REPORT");

    std::string studentId;
    std::cout << "Enter Student ID: ";
    std::cin >> studentId;

    Student* student = findStudent(studentId);
    if (student == nullptr) {
        std::cout << "Error: Student with ID " << studentId << " not found!" << std::endl;
        pauseScreen();
        return;
    }

    // Display comprehensive student report
    student->displayInfo();
    gradeManager.displayStudentGrades(studentId);

    pauseScreen();
}

void SchoolManagementSystem::generateSystemStatistics() {
    clearScreen();
    displayHeader("SYSTEM STATISTICS");

    std::cout << "=== School Management System Statistics ===" << std::endl;
    std::cout << "Total Students: " << database.getStudentCount() << std::endl;
    std::cout << "Total Teachers: " << database.getTeacherCount() << std::endl;
    std::cout << "Total Courses: " << database.getCourseCount() << std::endl;
    std::cout << "Total Grades: " << database.getGradeCount() << std::endl;

    // Calculate enrollment statistics
    auto courses = database.getAllCourses();
    int totalEnrollments = 0;
    for (const auto& course : courses) {
        totalEnrollments += course.getEnrolledCount();
    }
    std::cout << "Total Enrollments: " << totalEnrollments << std::endl;

    if (!courses.empty()) {
        std::cout << "Average Students per Course: "
                  << std::fixed << std::setprecision(1)
                  << static_cast<double>(totalEnrollments) / courses.size() << std::endl;
    }

    // Calculate GPA statistics
    auto students = database.getAllStudents();
    if (!students.empty()) {
        double totalGPA = 0.0;
        int studentsWithGPA = 0;

        for (const auto& student : students) {
            double gpa = student.getGPA();
            if (gpa > 0) {
                totalGPA += gpa;
                studentsWithGPA++;
            }
        }

        if (studentsWithGPA > 0) {
            std::cout << "Average System GPA: "
                      << std::fixed << std::setprecision(2)
                      << totalGPA / studentsWithGPA << std::endl;
        }
    }

    std::cout << "===========================================" << std::endl;

    pauseScreen();
}

// Placeholder implementations for missing functions
void SchoolManagementSystem::updateStudent() {
    std::cout << "Update Student functionality - Coming Soon!" << std::endl;
    pauseScreen();
}

void SchoolManagementSystem::deleteStudent() {
    std::cout << "Delete Student functionality - Coming Soon!" << std::endl;
    pauseScreen();
}

void SchoolManagementSystem::updateTeacher() {
    std::cout << "Update Teacher functionality - Coming Soon!" << std::endl;
    pauseScreen();
}

void SchoolManagementSystem::deleteTeacher() {
    std::cout << "Delete Teacher functionality - Coming Soon!" << std::endl;
    pauseScreen();
}

void SchoolManagementSystem::updateCourse() {
    std::cout << "Update Course functionality - Coming Soon!" << std::endl;
    pauseScreen();
}

void SchoolManagementSystem::deleteCourse() {
    std::cout << "Delete Course functionality - Coming Soon!" << std::endl;
    pauseScreen();
}

void SchoolManagementSystem::dropStudentFromCourse() {
    std::cout << "Drop Student from Course functionality - Coming Soon!" << std::endl;
    pauseScreen();
}

void SchoolManagementSystem::updateGrade() {
    std::cout << "Update Grade functionality - Coming Soon!" << std::endl;
    pauseScreen();
}

void SchoolManagementSystem::deleteGrade() {
    std::cout << "Delete Grade functionality - Coming Soon!" << std::endl;
    pauseScreen();
}

void SchoolManagementSystem::generateCourseReport() {
    std::cout << "Course Report functionality - Coming Soon!" << std::endl;
    pauseScreen();
}

void SchoolManagementSystem::handleTestDataMenu() {
    clearScreen();
    displayHeader("TEST DATA MANAGEMENT");
    std::cout << "1. Generate Test Data (150 records)" << std::endl;
    std::cout << "2. Clear All Data" << std::endl;
    std::cout << "3. View Database Statistics" << std::endl;
    std::cout << "0. Back to Main Menu" << std::endl;
    std::cout << "\nEnter your choice: ";

    int choice = getMenuChoice(0, 3);

    switch (choice) {
        case 1: {
            std::cout << "\nGenerating test data..." << std::endl;
            if (database.generateTestData()) {
                std::cout << "Test data generated successfully!" << std::endl;
                std::cout << "\nDatabase now contains:" << std::endl;
                std::cout << "- Students: " << database.getStudentCount() << std::endl;
                std::cout << "- Teachers: " << database.getTeacherCount() << std::endl;
                std::cout << "- Courses: " << database.getCourseCount() << std::endl;
                std::cout << "- Grades: " << database.getGradeCount() << std::endl;
            } else {
                std::cout << "Failed to generate test data!" << std::endl;
            }
            pauseScreen();
            break;
        }
        case 2: {
            if (confirmAction("Are you sure you want to clear all data? This cannot be undone!")) {
                std::cout << "\nClearing all data..." << std::endl;
                if (database.clearAllData()) {
                    std::cout << "All data cleared successfully!" << std::endl;
                } else {
                    std::cout << "Failed to clear data!" << std::endl;
                }
            }
            pauseScreen();
            break;
        }
        case 3: {
            std::cout << "\nDatabase Statistics:" << std::endl;
            std::cout << "===================" << std::endl;
            std::cout << "Students: " << database.getStudentCount() << std::endl;
            std::cout << "Teachers: " << database.getTeacherCount() << std::endl;
            std::cout << "Courses: " << database.getCourseCount() << std::endl;
            std::cout << "Grades: " << database.getGradeCount() << std::endl;
            std::cout << "Database file: school_management.db" << std::endl;
            std::cout << "Connection status: " << (database.isConnected() ? "Connected" : "Disconnected") << std::endl;
            pauseScreen();
            break;
        }
        case 0:
            break;
    }
}
