#include "student.h"
#include <algorithm>
#include <iomanip>

// Default constructor
Student::Student() : id(""), firstName(""), lastName(""), gradeLevel(0), 
                     studentClass(""), email(""), phoneNumber(""), gpa(0.0) {}

// Parameterized constructor
Student::Student(const std::string& id, const std::string& firstName, 
                 const std::string& lastName, int gradeLevel, 
                 const std::string& studentClass)
    : id(id), firstName(firstName), lastName(lastName), gradeLevel(gradeLevel),
      studentClass(studentClass), email(""), phoneNumber(""), gpa(0.0) {}

// Course management functions
bool Student::enrollInCourse(const std::string& courseId) {
    if (isEnrolledInCourse(courseId)) {
        return false; // Already enrolled
    }
    enrolledCourses.push_back(courseId);
    return true;
}

bool Student::dropCourse(const std::string& courseId) {
    auto it = std::find(enrolledCourses.begin(), enrolledCourses.end(), courseId);
    if (it != enrolledCourses.end()) {
        enrolledCourses.erase(it);
        return true;
    }
    return false; // Course not found
}

bool Student::isEnrolledInCourse(const std::string& courseId) const {
    return std::find(enrolledCourses.begin(), enrolledCourses.end(), courseId) 
           != enrolledCourses.end();
}

// Utility functions
void Student::displayInfo() const {
    std::cout << "\n=== Student Information ===" << std::endl;
    std::cout << "ID: " << id << std::endl;
    std::cout << "Name: " << getFullName() << std::endl;
    std::cout << "Grade Level: " << gradeLevel << std::endl;
    std::cout << "Class: " << studentClass << std::endl;
    std::cout << "Email: " << email << std::endl;
    std::cout << "Phone: " << phoneNumber << std::endl;
    std::cout << "GPA: " << std::fixed << std::setprecision(2) << gpa << std::endl;
    
    std::cout << "Enrolled Courses (" << enrolledCourses.size() << "): ";
    if (enrolledCourses.empty()) {
        std::cout << "None";
    } else {
        for (size_t i = 0; i < enrolledCourses.size(); ++i) {
            std::cout << enrolledCourses[i];
            if (i < enrolledCourses.size() - 1) std::cout << ", ";
        }
    }
    std::cout << std::endl;
    std::cout << "=========================" << std::endl;
}

bool Student::isValid() const {
    return !id.empty() && !firstName.empty() && !lastName.empty() && 
           gradeLevel >= 1 && gradeLevel <= 12;
}

// Operators
bool Student::operator==(const Student& other) const {
    return id == other.id;
}

std::ostream& operator<<(std::ostream& os, const Student& student) {
    os << "Student[ID: " << student.id << ", Name: " << student.getFullName() 
       << ", Grade: " << student.gradeLevel << ", GPA: " 
       << std::fixed << std::setprecision(2) << student.gpa << "]";
    return os;
}
