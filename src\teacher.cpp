#include "teacher.h"
#include <algorithm>
#include <iomanip>

// Default constructor
Teacher::Teacher() : id(""), firstName(""), lastName(""), department(""), 
                     email(""), phoneNumber(""), salary(0.0) {}

// Parameterized constructor
Teacher::Teacher(const std::string& id, const std::string& firstName,
                 const std::string& lastName, const std::string& department)
    : id(id), firstName(firstName), lastName(lastName), department(department),
      email(""), phoneNumber(""), salary(0.0) {}

// Subject management functions
bool Teacher::addSubject(const std::string& subject) {
    if (hasSubject(subject)) {
        return false; // Subject already exists
    }
    subjects.push_back(subject);
    return true;
}

bool Teacher::removeSubject(const std::string& subject) {
    auto it = std::find(subjects.begin(), subjects.end(), subject);
    if (it != subjects.end()) {
        subjects.erase(it);
        return true;
    }
    return false; // Subject not found
}

bool Teacher::hasSubject(const std::string& subject) const {
    return std::find(subjects.begin(), subjects.end(), subject) != subjects.end();
}

// Course management functions
bool Teacher::assignCourse(const std::string& courseId) {
    if (isAssignedToCourse(courseId)) {
        return false; // Already assigned
    }
    assignedCourses.push_back(courseId);
    return true;
}

bool Teacher::removeCourseAssignment(const std::string& courseId) {
    auto it = std::find(assignedCourses.begin(), assignedCourses.end(), courseId);
    if (it != assignedCourses.end()) {
        assignedCourses.erase(it);
        return true;
    }
    return false; // Course not found
}

bool Teacher::isAssignedToCourse(const std::string& courseId) const {
    return std::find(assignedCourses.begin(), assignedCourses.end(), courseId) 
           != assignedCourses.end();
}

// Utility functions
void Teacher::displayInfo() const {
    std::cout << "\n=== Teacher Information ===" << std::endl;
    std::cout << "ID: " << id << std::endl;
    std::cout << "Name: " << getFullName() << std::endl;
    std::cout << "Department: " << department << std::endl;
    std::cout << "Email: " << email << std::endl;
    std::cout << "Phone: " << phoneNumber << std::endl;
    std::cout << "Salary: $" << std::fixed << std::setprecision(2) << salary << std::endl;
    
    std::cout << "Subjects (" << subjects.size() << "): ";
    if (subjects.empty()) {
        std::cout << "None";
    } else {
        for (size_t i = 0; i < subjects.size(); ++i) {
            std::cout << subjects[i];
            if (i < subjects.size() - 1) std::cout << ", ";
        }
    }
    std::cout << std::endl;
    
    std::cout << "Assigned Courses (" << assignedCourses.size() << "): ";
    if (assignedCourses.empty()) {
        std::cout << "None";
    } else {
        for (size_t i = 0; i < assignedCourses.size(); ++i) {
            std::cout << assignedCourses[i];
            if (i < assignedCourses.size() - 1) std::cout << ", ";
        }
    }
    std::cout << std::endl;
    std::cout << "===========================" << std::endl;
}

bool Teacher::isValid() const {
    return !id.empty() && !firstName.empty() && !lastName.empty() && !department.empty();
}

// Operators
bool Teacher::operator==(const Teacher& other) const {
    return id == other.id;
}

std::ostream& operator<<(std::ostream& os, const Teacher& teacher) {
    os << "Teacher[ID: " << teacher.id << ", Name: " << teacher.getFullName() 
       << ", Department: " << teacher.department << ", Courses: " 
       << teacher.assignedCourses.size() << "]";
    return os;
}
